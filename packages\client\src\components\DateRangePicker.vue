<template>
	<div class="date-range-picker" v-click-outside="handleOutsideClick">
		<div class="inputs-container" @click.stop="togglePicker">
			<input link :id="id" v-model="displayValue" :placeholder="placeholder" readonly />
			<el-icon class="reset-icon" :class="{ 'visible': hasSelectedDates }" @click.stop="resetDates" title="重置日期">
				<RefreshRight />
			</el-icon>
			<SvgIcon :name="isPickerOpen ? 'calendar-sr-flaticon' : 'calendar-rr-flaticon'" class="date-icon"
				aria-hidden="true" />
		</div>
		<!--mousedown 事件在 click 之前触发-->
		<div v-if="isPickerOpen" class="picker-overlay" @click="closePicker">
			<div class="picker modal" @mousedown.stop @click.stop>
				<div class="picker-header">
					<button @click="changeMonth(-1)">&lt;</button>
					<span>{{ currentMonthLabel }}</span>
					<button @click="changeMonth(1)">&gt;</button>
				</div>
				<div class="picker-body">
					<div class="weekdays">
						<span v-for="day in weekdays" :key="day">{{ day }}</span>
					</div>
					<div class="days">
						<button v-for="(day, index) in daysInMonth" :key="formatDate(day.date)" :class="[
							'day',
							{ 'current-month': day.currentMonth },
							{ selected: isSelected(day.date) },
							{ 'in-range': isInRange(day.date) },
							{ today: isToday(day.date) },
							{ 'last-day': index === daysInMonth.length - 1 },
						]" @click="selectDate(day.date)" @mouseover="handleDayHover(day, index)" @mouseleave="handleDayLeave">
							{{ day.date.getDate() }}
						</button>
					</div>
					<div v-if="showNextMonthIndicator" class="next-month-indicator" @mouseover="handleNextMonthHover"
						@mouseleave="handleNextMonthLeave">
						》
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from "vue";
import SvgIcon from "@/components/SvgIcon.vue";
import { RefreshRight } from "@element-plus/icons-vue";
import { vClickOutside } from "@/utils/clickOutside";

const props = defineProps<{
	id: string;
	startDate: string;
	endDate: string;
	placeholder?: string;
}>();

const emit = defineEmits(["update:startDate", "update:endDate"]);

const localStartDate = ref(props.startDate);
const localEndDate = ref(props.endDate);

const displayValue = computed(() => {
	if (localStartDate.value && localEndDate.value) {
		return `${localStartDate.value} – ${localEndDate.value}`;
	}
	if (localStartDate.value) {
		return localStartDate.value;
	}
	return "";
});

const isPickerOpen = ref(false);
const currentMonth = ref(new Date());
const isSelectingStart = ref(true);

// 添加新的响应式变量用于预览
const previewDate = ref("");

// 添加临时存储变量
const tempStartDate = ref("");

const weekdays = ["日", "一", "二", "三", "四", "五", "六"];

const currentMonthLabel = computed(() => {
	const dateString = currentMonth.value.toLocaleString("zh-CN", {
		year: "numeric",
		month: "long",
	});
	return dateString.replace(/(\d+)年(\d+)月/, "$1 年 $2 月");
});

const daysInMonth = computed(() => {
	const year = currentMonth.value.getFullYear();
	const month = currentMonth.value.getMonth();
	const daysInMonth = new Date(year, month + 1, 0).getDate();
	const firstDayOfMonth = new Date(year, month, 1).getDay();

	const days = [];

	// Add days from previous month
	for (let i = 0; i < firstDayOfMonth; i++) {
		const date = new Date(year, month, -firstDayOfMonth + i + 1);
		days.push({ date, currentMonth: false });
	}

	// Add days of current month
	for (let i = 1; i <= daysInMonth; i++) {
		const date = new Date(year, month, i);
		days.push({ date, currentMonth: true });
	}

	// Add days from next month to fill the grid
	const remainingDays = 35 - days.length;
	for (let i = 1; i <= remainingDays; i++) {
		const date = new Date(year, month + 1, i);
		days.push({ date, currentMonth: false });
	}

	return days;
});

const togglePicker = () => {
	if (isPickerOpen.value) {
		closePicker();
	} else {
		openPicker();
	}
};

const openPicker = () => {
	isPickerOpen.value = true;
	isSelectingStart.value = true;
	if (!localStartDate.value) {
		currentMonth.value = new Date();
	} else {
		currentMonth.value = new Date(localStartDate.value);
	}
};

const closePicker = () => {
	isPickerOpen.value = false;
};

const handleOutsideClick = (event: MouseEvent) => {
	if (isPickerOpen.value && !(event.target as HTMLElement).closest(".picker")) {
		closePicker();
	}
};

const selectDate = (date: Date) => {
	if (isSelectingStart.value) {
		// 选择开始日期
		tempStartDate.value = formatDate(date);
		isSelectingStart.value = false;
		previewDate.value = "";
	} else {
		const selectedDate = formatDate(date);
		const startDateObj = new Date(tempStartDate.value);
		const endDateObj = new Date(selectedDate);

		if (endDateObj < startDateObj) {
			// 如果选择的结束日期早于开始日期，将当前选择作为开始日期
			localStartDate.value = selectedDate;
			localEndDate.value = tempStartDate.value;
		} else {
			// 正常情况：开始日期早于结束日期
			localStartDate.value = tempStartDate.value;
			localEndDate.value = selectedDate;
		}

		// 只有当两个日期都选择完成时才触发更新
		emit("update:startDate", localStartDate.value);
		emit("update:endDate", localEndDate.value);

		previewDate.value = "";
		tempStartDate.value = "";
		isPickerOpen.value = false;
	}
};

const changeMonth = (delta: number) => {
	const newMonth = new Date(currentMonth.value);
	newMonth.setMonth(newMonth.getMonth() + delta);
	currentMonth.value = newMonth;
};

const formatDate = (date: Date): string => {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, "0");
	const day = String(date.getDate()).padStart(2, "0");
	return `${year}-${month}-${day}`;
};

const isSelected = (date: Date): boolean => {
	const dateStr = formatDate(date);
	// 如果正在重新选择（tempStartDate有值），则只考虑临时数据
	if (tempStartDate.value) {
		return dateStr === tempStartDate.value;
	}
	// 否则使用已保存的日期范围
	return dateStr === localStartDate.value || dateStr === localEndDate.value;
};

const isInRange = (date: Date): boolean => {
	// 如果正在重新选择（tempStartDate有值），则只考虑临时数据
	if (tempStartDate.value) {
		// 如果有预览日期，则使用tempStartDate和previewDate
		if (previewDate.value) {
			const start = new Date(tempStartDate.value);
			const end = new Date(previewDate.value);
			const [earlierDate, laterDate] = start < end ? [start, end] : [end, start];
			return date > earlierDate && date < laterDate;
		}
		// 只有tempStartDate，没有范围可显示
		return false;
	}

	// 不是重新选择过程，使用已保存的日期范围
	if (!localStartDate.value || !localEndDate.value) return false;

	const start = new Date(localStartDate.value);
	const end = new Date(localEndDate.value);
	const [earlierDate, laterDate] = start < end ? [start, end] : [end, start];
	return date > earlierDate && date < laterDate;
};

const isToday = (date: Date): boolean => {
	const today = new Date();
	return (
		date.getDate() === today.getDate() &&
		date.getMonth() === today.getMonth() &&
		date.getFullYear() === today.getFullYear()
	);
};

watch(
	() => props.startDate,
	(newValue) => {
		localStartDate.value = newValue || "";
	},
);

watch(
	() => props.endDate,
	(newValue) => {
		localEndDate.value = newValue || "";
	},
);

const showNextMonthIndicator = ref(false);
const dayHoverTimer = ref<ReturnType<typeof setTimeout> | null>(null);
const nextMonthHoverTimer = ref<ReturnType<typeof setTimeout> | null>(null);
const indicatorHideTimer = ref<ReturnType<typeof setTimeout> | null>(null);

const handleDayHover = (
	day: { date: Date; currentMonth: boolean },
	index: number,
) => {
	previewEndDate(day.date);
	if (index === daysInMonth.value.length - 1) {
		if (indicatorHideTimer.value) {
			clearTimeout(indicatorHideTimer.value);
			indicatorHideTimer.value = null;
		}
		dayHoverTimer.value = setTimeout(() => {
			showNextMonthIndicator.value = true;
		}, 500); // 500ms delay before showing the indicator
	}
};

const handleDayLeave = () => {
	if (dayHoverTimer.value) {
		clearTimeout(dayHoverTimer.value);
		dayHoverTimer.value = null;
	}
	// 设置一个延迟来隐藏指示器
	indicatorHideTimer.value = setTimeout(() => {
		showNextMonthIndicator.value = false;
	}, 300); // 300ms delay before hiding the indicator
};

const handleNextMonthHover = () => {
	if (indicatorHideTimer.value) {
		clearTimeout(indicatorHideTimer.value);
		indicatorHideTimer.value = null;
	}
	nextMonthHoverTimer.value = setTimeout(() => {
		changeMonth(1);
	}, 500); // 500ms delay before changing to next month
};

const handleNextMonthLeave = () => {
	if (nextMonthHoverTimer.value) {
		clearTimeout(nextMonthHoverTimer.value);
		nextMonthHoverTimer.value = null;
	}
};

const previewEndDate = (date: Date) => {
	if (!isSelectingStart.value && tempStartDate.value) {
		// 更新预览日期，不管日期大小关系如何
		previewDate.value = formatDate(date);
	}
};

onUnmounted(() => {
	if (dayHoverTimer.value) clearTimeout(dayHoverTimer.value);
	if (nextMonthHoverTimer.value) clearTimeout(nextMonthHoverTimer.value);
	if (indicatorHideTimer.value) clearTimeout(indicatorHideTimer.value);
});

// 添加计算属性检查是否有选中日期
const hasSelectedDates = computed(() => {
	return Boolean(localStartDate.value || localEndDate.value);
});

// 添加重置方法
const resetDates = (event: Event) => {
	event.stopPropagation();
	localStartDate.value = "";
	localEndDate.value = "";
	emit("update:startDate", "");
	emit("update:endDate", "");
	closePicker();
};
</script>

<style scoped>
.date-range-picker {
	position: relative;
}

.inputs-container {
	display: flex;
	align-items: center;
	box-shadow: 0 0 0 1px var(--el-border-color) inset;
	border-radius: var(--el-border-radius-base);
	padding: 4px 12px;
	cursor: pointer;
	background-color: var(--el-fill-color-light);
	transition: all 0.25s ease;
	height: 32px;
}

.inputs-container:hover:not(:focus-within) {
	box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
}

.inputs-container:focus-within {
	box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.inputs-container input {
	border: none;
	outline: none;
	width: 154px;
	padding: 4px;
	cursor: pointer;
	background-color: transparent;
	color: var(--el-text-color-primary);
	height: 24px;
	line-height: 24px;
}

.date-icon {
	width: 16px;
	height: 16px;
	color: var(--el-text-color-regular);
	transition: color 0.25s ease;

	&:hover {
		color: var(--el-color-primary-dark-2);
	}
}

.picker {
	position: absolute;
	top: calc(100% + 4px);
	left: 0;
	z-index: 1000;
	padding: 16px;
	background-color: var(--el-fill-color-light);
	border: 1px solid var(--el-border-color);
	border-radius: var(--el-border-radius-base);
	box-shadow: 0 2px 12px rgba(var(--shadow-color), 0.15);
}

.picker-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
}

.picker-header button {
	font-size: 1.2rem;
	line-height: 1;
	background: none;
	border: 1px solid var(--el-border-color);
	border-radius: var(--el-border-radius-base);
	padding: 4px 12px 8px;
	color: var(--el-text-color-primary);
	cursor: pointer;
	transition: all 0.25s ease;
}

.picker-header button:hover {
	border-color: var(--el-color-primary);
	color: var(--el-color-primary);
}

.picker-header span {
	font-size: 16px;
	color: var(--el-text-color-primary);
}

.weekdays {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	text-align: center;
	margin-bottom: 8px;
	color: var(--text-color-secondary);
}

.days {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	gap: 4px;
}

.day {
	aspect-ratio: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid transparent;
	background: none;
	cursor: pointer;
	border-radius: 25px;
	font-size: 14px;
	color: var(--el-text-color-primary);
	transition: all 0.25s ease;
}

.day:not(.current-month) {
	color: var(--text-color-secondary);
	opacity: 0.5;
}

.day.selected {
	background-color: var(--el-color-primary);
	color: var(--el-color-primary-light-7);
}

.day.in-range:not(.selected) {
	background-color: var(--el-color-primary-light-7);
	color: var(--el-color-primary);
}

.day.today {
	font-weight: 500;
	border-color: var(--el-color-primary);
}

.day:hover:not(.selected) {
	background-color: var(--hover-bg-color);
}

.next-month-indicator {
	position: absolute;
	right: -24px;
	bottom: 20px;
	font-size: 20px;
	color: var(--el-color-primary);
	cursor: pointer;
	opacity: 0.8;
	transition: opacity 0.25s ease;
}

.next-month-indicator:hover {
	opacity: 1;
}

@keyframes float {

	0%,
	100% {
		transform: translateX(0);
	}

	50% {
		transform: translateX(4px);
	}
}

.next-month-indicator {
	animation: float 2s ease-in-out infinite;
}

/* 重置图标样式 */
.reset-icon {
	font-size: 20px;
	margin-right: 8px;
	cursor: default;
	transition: all 0.25s ease;
	color: var(--el-text-color-regular);
	opacity: 0;
	visibility: hidden;
}

.reset-icon.visible {
	opacity: 1;
	visibility: visible;
	cursor: pointer;

	&:hover {
		color: var(--el-color-primary-dark-2);
	}
}

/* Mobile responsiveness */
@media (max-width: 768px) {
	.picker-overlay {
		/* 全屏蒙版 */
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1999;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20px;
	}

	.picker {
		/* 居中的选择器 */
		position: relative;
		left: auto;
		top: auto;
		transform: none;
		width: 100%;
		max-width: 360px;
		padding: 12px;
		z-index: 2000;
	}

	.day {
		font-size: 13px;
		padding: 0.5rem 1rem;
	}

	.days {
		gap: 2px;
	}
}
</style>

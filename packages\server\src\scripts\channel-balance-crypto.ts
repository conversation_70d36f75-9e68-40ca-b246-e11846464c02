import { PrismaClient } from "@prisma/client";
import { encrypt, decrypt } from "@/utils/encryption.js";
import { getDatabaseUrl } from "@/config/defaultParams.js";
import { isTradingPlatform } from "@/config/configManager.js";
import readline from "node:readline";

/**
 * 通道余额字段加解密工具
 *
 * ⚠️  高危操作警告：此脚本仅限初次迁移数据时使用！
 * ⚠️  仅限交易台使用，仅限下午11点到次日凌晨6点使用！
 *
 * 用法：
 * npm run channel-balance-crypto <操作>
 *
 * 操作:
 *   encrypt    加密所有通道余额字段
 *   decrypt    解密所有通道余额字段
 */

// 创建Prisma客户端
const prisma = new PrismaClient({
	datasources: {
		db: {
			url: getDatabaseUrl(),
		},
	},
	log: ["error"],
});

// 余额字段列表
const BALANCE_FIELDS = ["balance_cny", "balance_hkd", "balance_usd"];

/**
 * 检查当前时间是否在允许的维护时间窗口内（23:00-06:00）
 */
function isInMaintenanceWindow(): boolean {
	const now = new Date();
	const hour = now.getHours();

	// 允许的时间：23:00-06:00
	return hour >= 23 || hour < 6;
}

/**
 * 检查运行环境和时间限制
 */
function checkEnvironmentAndTime(): void {
	// 检查是否为交易台环境
	if (!isTradingPlatform()) {
		console.error("❌ 此脚本仅限交易台环境使用！");
		process.exit(1);
	}

	// 检查时间窗口
	if (!isInMaintenanceWindow()) {
		console.error("❌ 此脚本仅限在维护时间窗口内使用（23:00-06:00）！");
		console.error(
			"当前时间不在允许的维护时间范围内，请在下午11点到次日凌晨6点之间执行。",
		);
		process.exit(1);
	}

	console.info("✅ 环境和时间检查通过");
}

/**
 * 列出所有通道的明文余额
 */
async function listPlaintextBalances(): Promise<void> {
	try {
		console.info("📋 当前所有通道的明文余额：");

		const channels = await prisma.channels.findMany({
			select: {
				channel_id: true,
				name: true,
				balance_cny: true,
				balance_hkd: true,
				balance_usd: true,
				is_locked: true,
			},
		});

		if (channels.length === 0) {
			console.info("未找到任何通道记录");
			return;
		}

		console.log("\n================= 通道余额清单 =================");
		for (const channel of channels) {
			const cnyBalance = String(channel.balance_cny);
			const hkdBalance = String(channel.balance_hkd);
			const usdBalance = String(channel.balance_usd);
			const lockStatus = channel.is_locked ? "🔒 已锁定" : "🔓 未锁定";

			console.log(`\n通道ID: ${channel.channel_id}`);
			console.log(`通道名: ${channel.name}`);
			console.log(`状态: ${lockStatus}`);
			console.log(`CNY余额: ${cnyBalance}`);
			console.log(`HKD余额: ${hkdBalance}`);
			console.log(`USD余额: ${usdBalance}`);
			console.log("─".repeat(50));
		}
		console.log("===============================================\n");
	} catch (error) {
		console.error("获取余额清单失败:", error);
		throw error;
	}
}

/**
 * 二次确认函数
 */
async function confirmOperation(action: string): Promise<boolean> {
	const rl = readline.createInterface({
		input: process.stdin,
		output: process.stdout,
	});

	return new Promise((resolve) => {
		console.warn("⚠️  =================  高危操作警告  =================");
		console.warn(
			`⚠️  即将${action === "encrypt" ? "加密" : "解密"}所有通道余额字段！`,
		);
		console.warn("⚠️  此操作仅限初次迁移数据时使用！");
		console.warn("⚠️  请确保已备份数据库！");
		console.warn("⚠️  请确保已关闭所有交易台应用进程！");
		console.warn("⚠️  操作不可逆，请谨慎确认！");
		console.warn("⚠️  ===============================================");

		rl.question('\n确认执行操作？请输入 "CONFIRM" 来确认: ', (answer) => {
			rl.close();
			resolve(answer === "CONFIRM");
		});
	});
}

/**
 * 处理单个通道的所有余额字段
 */
async function processChannelBalances(
	channelId: string,
	balances: Record<string, string>,
	action: "encrypt" | "decrypt",
): Promise<{ processed: number; skipped: number }> {
	let processed = 0;
	let skipped = 0;
	const updates: Record<string, string> = {};

	for (const field of BALANCE_FIELDS) {
		const value = balances[field];
		if (!value) continue;

		try {
			let newValue: string;

			if (action === "encrypt") {
				try {
					// 先尝试解密，看是否已经是加密的
					decrypt(value);
					console.debug(`通道 ${channelId} 的 ${field} 已经是加密的，跳过`);
					skipped++;
					continue;
				} catch {
					// 解密失败，说明是明文，可以加密
					newValue = encrypt(value);
				}
			} else {
				try {
					// 尝试解密
					newValue = decrypt(value);
				} catch {
					// 解密失败，可能已经是明文
					console.debug(`通道 ${channelId} 的 ${field} 已经是明文，跳过`);
					skipped++;
					continue;
				}
			}

			updates[field] = newValue;
			processed++;
		} catch (e) {
			console.error(
				`处理通道 ${channelId} 的 ${field} 字段时出错: ${e instanceof Error ? e.message : String(e)}`,
			);
		}
	}

	// 批量更新
	if (Object.keys(updates).length > 0) {
		await prisma.channels.update({
			where: { channel_id: channelId },
			data: updates,
		});
	}

	return { processed, skipped };
}

/**
 * 处理所有通道余额
 */
async function processAllChannelBalances(
	action: "encrypt" | "decrypt",
): Promise<void> {
	try {
		console.info(
			`开始${action === "encrypt" ? "加密" : "解密"}所有通道余额字段...`,
		);

		// 如果是加密操作，先列出所有明文余额
		if (action === "encrypt") {
			await listPlaintextBalances();
		}

		// 获取所有通道记录
		const channels = await prisma.channels.findMany({
			select: {
				channel_id: true,
				balance_cny: true,
				balance_hkd: true,
				balance_usd: true,
			},
		});

		const totalChannels = channels.length;
		console.info(`找到 ${totalChannels} 个通道需要处理`);

		if (totalChannels === 0) {
			console.info("没有通道需要处理");
			return;
		}

		let totalProcessed = 0;
		let totalSkipped = 0;
		let channelCount = 0;

		for (const channel of channels) {
			try {
				const { processed, skipped } = await processChannelBalances(
					channel.channel_id,
					{
						balance_cny: String(channel.balance_cny),
						balance_hkd: String(channel.balance_hkd),
						balance_usd: String(channel.balance_usd),
					},
					action,
				);

				totalProcessed += processed;
				totalSkipped += skipped;
				channelCount++;

				if (channelCount % 10 === 0) {
					console.info(`已处理 ${channelCount}/${totalChannels} 个通道...`);
				}
			} catch (e) {
				console.error(
					`处理通道 ${channel.channel_id} 时出错: ${e instanceof Error ? e.message : String(e)}`,
				);
			}
		}

		console.info(
			`处理完成: ${totalProcessed} 个字段已${action === "encrypt" ? "加密" : "解密"}, ${totalSkipped} 个字段已跳过`,
		);
	} catch (error) {
		console.error("处理通道余额时出错:", error);
		throw error;
	}
}

/**
 * 主函数
 */
async function main() {
	const args = process.argv.slice(2);

	if (args.length < 1) {
		const helpText = `
⚠️  通道余额加解密工具（高危操作）

⚠️  重要限制：
   - 仅限交易台环境使用
   - 仅限维护时间窗口使用（23:00-06:00）
   - 执行前必须关闭交易台应用进程

用法:
  pnpm channel-balance-crypto <操作>

操作:
  decrypt    解密所有通道余额字段
  encrypt    加密所有通道余额字段

⚠️  警告：此脚本仅限初次迁移数据时使用！
⚠️  执行前请确保已备份数据库！
⚠️  执行前请确保已关闭所有交易台应用进程！

示例:
  pnpm channel-balance-crypto encrypt
  pnpm channel-balance-crypto decrypt
    `;
		console.log(helpText);
		process.exit(0);
	}

	// 检查环境和时间限制
	checkEnvironmentAndTime();

	// 去掉"--"分隔符，避免在Linux下被识别为选项
	const action = args[0];

	if (action !== "encrypt" && action !== "decrypt") {
		console.error("错误: 操作必须是 'encrypt' 或 'decrypt'");
		process.exit(1);
	}

	// 二次确认
	const confirmed = await confirmOperation(action);
	if (!confirmed) {
		console.info("操作已取消");
		process.exit(0);
	}

	try {
		// 创建备份
		console.info("创建 channels 表备份...");
		const backupTableName = `channels_crypto_backup_${Date.now()}`;
		await prisma.$executeRawUnsafe(
			`CREATE TABLE ${backupTableName} AS SELECT * FROM channels`,
		);
		console.info(`备份已创建: ${backupTableName}`);

		// 处理余额字段
		await processAllChannelBalances(action as "encrypt" | "decrypt");

		console.info("✅ 操作成功完成！");
		console.info("💡 请记得重新启动交易台应用进程");
		process.exit(0);
	} catch (error) {
		console.error("❌ 操作失败:", error);
		process.exit(1);
	} finally {
		await prisma.$disconnect();
	}
}

main();

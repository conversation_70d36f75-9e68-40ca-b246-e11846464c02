/**
 * INK数据同步系统 - 常量定义
 *
 * 本文件定义了INK数据同步系统中使用的所有常量，包括：
 * - Redis键名规范
 * - 锁配置参数
 * - 交易时间定义
 * - 作业配置
 * - 作业标识符
 *
 * 设计原则：
 * 1. 集中管理所有常量，避免硬编码分散
 * 2. 使用语义化命名，便于理解和维护
 * 3. 按功能模块分组，层次清晰
 */

// ============================================================================
// Redis 键名空间定义
// ============================================================================
/**
 * Redis键名规范
 *
 * 采用分层命名空间设计：ink:{模块}:{功能}:{详细标识}
 * 好处：便于管理、避免冲突、支持批量操作
 */
export const REDIS_KEYS = {
	// 数据缓存相关
	INK_SWING_DATA: "ink:swing:data", // INK波动数据缓存
	INK_PRICE_QUOTES_PREFIX: "ink:quotes", // 价格报价缓存前缀

	// 分布式锁相关 - 防止多实例并发执行
	INK_SYNC_LOCK: "ink:sync:lock", // 初始化同步锁
	INK_DATA_UPDATE_LOCK: "ink:data:update:lock", // 数据更新锁
	INK_QUOTES_UPDATE_LOCK: "ink:quotes:update:lock", // 报价更新锁
	INK_CONFIG_UPDATE_LOCK: "ink:config:update:lock", // 配置更新锁
	INK_MANAGE_MARKET_LOCK: "ink:manage:market:lock", // 交易时段管理锁
	INK_MANAGE_PREMARKET_LOCK: "ink:manage:premarket:lock", // 盘前时段管理锁

	// 执行历史记录相关
	INK_SYNC_RECORD: "ink:sync:last_execution", // 最后执行记录
	INK_EXECUTION_HISTORY: "ink:execution:history", // 执行历史列表
} as const;

// ============================================================================
// 分布式锁配置
// ============================================================================
/**
 * 分布式锁配置参数
 *
 * 用于协调多个服务实例，防止重复执行关键任务
 * 配置说明：
 * - EXPIRY: 锁的默认过期时间，防止死锁
 * - RENEWAL_INTERVAL: 锁续期间隔，保持锁的活跃状态
 * - RECENT_EXECUTION_THRESHOLD: 最近执行阈值，避免频繁重复执行
 */
export const LOCK_CONFIG = {
	EXPIRY: 60 * 5, // 锁过期时间：5分钟（防止进程异常导致的死锁）
	RECENT_EXECUTION_THRESHOLD: 60 * 3, // 最近执行阈值：3分钟内不重复执行
	RENEWAL_INTERVAL: 60 * 2, // 锁续期间隔：每2分钟续期一次
	EXECUTION_RECORD_EXPIRY: 60 * 60 * 24 * 3, // 执行记录保存时间：3天
} as const;

// ============================================================================
// 交易时间配置
// ============================================================================
/**
 * 中国股市交易时间定义
 *
 * 时间格式：HHMM（如925表示9:25）
 *
 * 交易时段：
 * - 盘前：8:30-9:30（准备阶段，低频更新外部报价）
 * - 上午：9:25-11:30（交易时段，高频更新内部数据）
 * - 午休：11:30-13:00（暂停高频更新）
 * - 下午：13:00-15:00（交易时段，高频更新内部数据）
 * - 盘后：15:00之后（低频维护更新）
 */
export const TRADING_TIME = {
	// 上午交易时段
	MORNING_START: 925, // 上午开盘：9:25（集合竞价结束）
	MORNING_END: 1130, // 上午收盘：11:30（午休开始）

	// 下午交易时段
	AFTERNOON_START: 1300, // 下午开盘：13:00（午休结束）
	AFTERNOON_END: 1500, // 下午收盘：15:00（全天交易结束）

	// 盘前时段（外部报价更新）
	PREMARKET_START: 830, // 盘前开始：8:30
	PREMARKET_END: 930, // 盘前结束：9:30（正式开盘前）

	// 兼容性配置（保持向后兼容）
	TRADING_START: 925, // 整体交易开始时间
	TRADING_END: 1500, // 整体交易结束时间

	// 延迟清理配置
	CLEANUP_DELAY_SECONDS: 5, // 交易结束后延迟清理时间，确保最后一次更新完成
} as const;

// ============================================================================
// 作业执行配置
// ============================================================================
/**
 * 后台作业配置参数
 *
 * 根据不同时段和数据重要性调整更新频率：
 * - 交易时段：高频更新内部数据（5秒）
 * - 盘前时段：中频更新外部报价（5分钟）
 * - 非交易时段：低频维护更新（30分钟）
 */
export const JOB_CONFIG = {
	HIGH_FREQ_UPDATE_INTERVAL: 5000, // 高频更新间隔：5秒（交易时段内部数据）
	PREMARKET_UPDATE_INTERVAL: 300000, // 盘前更新间隔：5分钟（外部价格报价）
	CACHE_EXPIRATION: 60 * 60 * 24, // 缓存过期时间：24小时
} as const;

// ============================================================================
// 作业类型定义
// ============================================================================
/**
 * INK数据同步作业类型枚举
 *
 * 命名规范：inksync-{功能描述}
 * 作业分类：
 * 1. 数据更新类：定期获取和更新各类数据
 * 2. 管理调度类：根据交易时段动态调整作业频率
 * 3. 初始化类：系统启动时的初始化任务
 */
export const INK_DATA_SYNC_JOBS = {
	// 数据更新作业
	UPDATE_INK_DATA: "inksync-update-data", // 更新INK波动数据
	UPDATE_PRICE_QUOTES: "inksync-update-price-quotes", // 更新价格报价数据
	UPDATE_BUSINESS_CONFIG: "inksync-update-business-config", // 更新业务配置参数

	// 动态调度管理作业
	MANAGE_MARKET_TIME_UPDATES: "inksync-manage-market-time-updates", // 管理交易时段高频更新
	MANAGE_PREMARKET_PRICE_QUOTES: "inksync-manage-premarket-price-quotes", // 管理盘前报价更新

	// 高频执行作业（动态创建/删除）
	HIGH_FREQ_INK_DATA_UPDATE: "inksync-high-freq-data-update", // 高频INK数据更新（交易时段）
	PREMARKET_PRICE_QUOTES_UPDATE: "inksync-premarket-price-quotes-update", // 盘前价格报价更新

	// 系统初始化作业
	INITIALIZE_INK_DATA_SCHEDULES: "inksync-initialize-schedules", // 初始化所有调度任务
} as const;

// ============================================================================
// 作业实例标识符
// ============================================================================
/**
 * 可重复作业的唯一标识符
 *
 * 用于管理动态创建的重复作业，便于后续的查找、修改和删除操作
 */
export const JOB_IDS = {
	HIGH_FREQ: "high-freq-ink-data-update", // 高频数据更新作业ID
	PREMARKET: "premarket-price-quotes-update", // 盘前报价更新作业ID
} as const;

// ============================================================================
// 交易时间工具类
// ============================================================================
/**
 * 交易时间处理工具集
 *
 * 提供时间格式转换和cron表达式生成功能
 * 设计目标：将数字时间（如925）转换为可用的调度配置
 */
export const TradingTimeUtils = {
	/**
	 * 解析数字时间格式
	 * @param timeValue 时间数字，如 925 表示 9:25
	 * @returns {hour: number, minute: number} 小时和分钟对象
	 *
	 * 示例：925 -> {hour: 9, minute: 25}
	 */
	parseTime(timeValue: number): { hour: number; minute: number } {
		const hour = Math.floor(timeValue / 100); // 取整数部分作为小时
		const minute = timeValue % 100; // 取余数部分作为分钟
		return { hour, minute };
	},

	/**
	 * 转换为cron表达式的时间部分
	 * @param timeValue 时间数字，如 925 表示 9:25
	 * @returns cron时间字符串，格式为 "分 时"
	 *
	 * 示例：925 -> "25 9"（用于cron表达式）
	 */
	toCronTime(timeValue: number): string {
		const { hour, minute } = this.parseTime(timeValue);
		return `${minute} ${hour}`;
	},

	/**
	 * 获取关键交易时间点的cron表达式集合
	 * @returns 预定义的cron表达式对象
	 *
	 * 返回格式：每个表达式都是完整的cron格式 "分 时 * * *"
	 * 用途：自动在关键时间点触发调度管理任务
	 */
	getCronExpressions() {
		return {
			// 盘前时段控制点
			premarketStart: `${this.toCronTime(TRADING_TIME.PREMARKET_START)} * * *`, // 8:30 盘前开始
			premarketEnd: `${this.toCronTime(TRADING_TIME.PREMARKET_END)} * * *`, // 9:30 盘前结束

			// 交易时段控制点
			morningStart: `${this.toCronTime(TRADING_TIME.MORNING_START)} * * *`, // 9:25 上午开盘
			morningEnd: `${this.toCronTime(TRADING_TIME.MORNING_END)} * * *`, // 11:30 上午收盘
			afternoonStart: `${this.toCronTime(TRADING_TIME.AFTERNOON_START)} * * *`, // 13:00 下午开盘
			afternoonEnd: `${this.toCronTime(TRADING_TIME.AFTERNOON_END)} * * *`, // 15:00 下午收盘

			// 特殊时间点
			morningStartPlus1: `${this.toCronTime(TRADING_TIME.MORNING_START + 1)} * * *`, // 9:26 开盘后1分钟
		};
	},
} as const;

# 总控端部署指南

## 🚀 快速开始

### 1. 环境准备

**系统要求:**
- Node.js 18+
- Docker & Docker Compose
- pnpm 8+
- Git

**检查环境:**
```bash
node --version    # >= 18
docker --version  # >= 20
pnpm --version    # >= 8
```

### 2. 克隆并初始化项目

```bash
# 克隆项目
git clone <your-repo> ink-control-center
cd ink-control-center

# 给设置脚本添加执行权限
chmod +x scripts/setup-control-center.sh

# 安装依赖
pnpm install

# 初始化数据库（开发环境）
cd packages/control-center-backend
pnpm prisma:generate
pnpm prisma:push
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp packages/control-center-backend/env.example packages/control-center-backend/.env

# 编辑配置
nano packages/control-center-backend/.env
```

**关键配置项:**
```env
# 总控端配置
NODE_ENV=production
PORT=3001
JWT_SECRET=your-super-secret-jwt-key

# 数据库
DATABASE_URL="file:./control-center.db"

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# 前端
FRONTEND_URL=http://localhost:5173
```

### 4. 本地开发启动

```bash
# 启动后端开发服务
cd packages/control-center-backend
pnpm dev

# 新终端：启动前端开发服务
cd packages/control-center-frontend
pnpm dev
```

访问: http://localhost:5173

## 🐳 Docker 部署

### 1. 使用 Docker Compose

```bash
# 构建并启动总控端
docker-compose -f docker-compose.control-center.yml up -d

# 查看日志
docker-compose -f docker-compose.control-center.yml logs -f
```

### 2. 环境变量配置

创建 `.env` 文件：
```env
# 项目名称
COMPOSE_PROJECT_NAME=ink-control-center

# 总控端域名
CONTROL_CENTER_DOMAIN=control.your-domain.com

# 安全配置
CONTROL_CENTER_JWT_SECRET=your-super-secret-jwt-key
CONTROL_CENTER_REDIS_PASSWORD=your-redis-password

# 端口配置
CONTROL_CENTER_HTTP_PORT=80
CONTROL_CENTER_TRAEFIK_PORT=8080

# API配置
CONTROL_CENTER_API_URL=http://control.your-domain.com/api
CONTROL_CENTER_WS_URL=ws://control.your-domain.com
```

### 3. 生产环境部署

**步骤 1: 服务器准备**
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

**步骤 2: 部署配置**
```bash
# 创建部署目录
sudo mkdir -p /opt/ink-control-center
cd /opt/ink-control-center

# 克隆项目
git clone <your-repo> .

# 配置环境变量
sudo cp docker-compose.control-center.yml docker-compose.yml
sudo nano .env
```

**步骤 3: 启动服务**
```bash
# 构建并启动
sudo docker-compose up -d

# 查看状态
sudo docker-compose ps
sudo docker-compose logs -f
```

## 🔧 高级配置

### 1. SSL/TLS 配置

**使用 Let's Encrypt:**
```yaml
# docker-compose.yml 中添加
services:
  control-center-traefik:
    command:
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.storage=/etc/traefik/certs/acme.json"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web"
    labels:
      - "traefik.http.routers.control-center.tls.certresolver=letsencrypt"
```

### 2. 数据备份配置

**自动备份脚本:**
```bash
#!/bin/bash
# backup-control-center.sh

BACKUP_DIR="/opt/backups/control-center"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
docker-compose exec control-center-backend sh -c "
  cd /app/data && 
  sqlite3 control-center.db '.backup /tmp/backup.db' &&
  cp /tmp/backup.db /app/backups/control-center-$DATE.db
"

# 备份配置文件
cp .env $BACKUP_DIR/env-$DATE

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "env-*" -mtime +30 -delete

echo "备份完成: $DATE"
```

**添加到 crontab:**
```bash
# 每天凌晨2点备份
0 2 * * * /opt/ink-control-center/backup-control-center.sh >> /var/log/control-center-backup.log 2>&1
```

### 3. 监控告警配置

**Prometheus 监控:**
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
  
  grafana:
    image: grafana/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
```

## 🔐 安全配置

### 1. 防火墙配置

```bash
# 开启防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow 22

# 允许HTTP/HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 只允许特定IP访问管理端口
sudo ufw allow from YOUR_IP to any port 8080
```

### 2. SSH密钥管理

```bash
# 生成SSH密钥对（在总控端服务器）
ssh-keygen -t rsa -b 4096 -C "<EMAIL>"

# 复制公钥到目标服务器
ssh-copy-id -i ~/.ssh/id_rsa.pub user@target-server

# 配置无密码登录
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub
```

### 3. 网络安全

**VPN配置（可选）:**
```bash
# 安装 WireGuard
sudo apt install wireguard

# 生成密钥
wg genkey | tee privatekey | wg pubkey > publickey

# 配置 VPN
sudo nano /etc/wireguard/wg0.conf
```

## 📊 监控与维护

### 1. 日志管理

**查看日志:**
```bash
# 实时查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f control-center-backend

# 查看错误日志
docker-compose logs | grep ERROR
```

**日志轮转配置:**
```json
// docker-compose.yml
services:
  control-center-backend:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 2. 性能监控

**系统资源监控:**
```bash
# CPU、内存使用情况
docker stats

# 磁盘使用情况
df -h

# 网络连接
netstat -tlnp
```

### 3. 健康检查

**服务健康检查:**
```bash
# 检查服务状态
curl http://localhost:3001/health

# 检查数据库连接
docker-compose exec control-center-backend pnpm prisma:studio
```

## 🔄 更新部署

### 1. 滚动更新

```bash
# 拉取最新代码
git pull origin main

# 重新构建镜像
docker-compose build --no-cache

# 滚动更新服务
docker-compose up -d --force-recreate
```

### 2. 零停机更新

```bash
#!/bin/bash
# rolling-update.sh

echo "开始滚动更新..."

# 备份当前版本
docker tag ink-control-center_control-center-backend:latest ink-control-center_control-center-backend:backup

# 构建新版本
docker-compose build

# 更新后端服务
docker-compose up -d --no-deps control-center-backend

# 等待服务就绪
sleep 30

# 健康检查
if curl -f http://localhost:3001/health; then
    echo "后端更新成功"
    # 更新前端服务
    docker-compose up -d --no-deps control-center-frontend
    echo "更新完成"
else
    echo "后端更新失败，回滚..."
    docker tag ink-control-center_control-center-backend:backup ink-control-center_control-center-backend:latest
    docker-compose up -d --no-deps control-center-backend
fi
```

## 🚨 故障排除

### 常见问题

**1. 数据库连接失败**
```bash
# 检查数据库文件权限
ls -la packages/control-center-backend/
chmod 644 packages/control-center-backend/control-center.db

# 重新生成 Prisma 客户端
cd packages/control-center-backend
pnpm prisma:generate
```

**2. Redis 连接失败**
```bash
# 检查 Redis 服务
docker-compose ps control-center-redis

# 测试 Redis 连接
docker-compose exec control-center-redis redis-cli ping
```

**3. 前端无法连接后端**
```bash
# 检查网络配置
docker network ls
docker network inspect control-center_control-center-network

# 检查环境变量
docker-compose exec control-center-frontend env | grep VITE
```

### 紧急恢复

**快速回滚:**
```bash
# 停止当前服务
docker-compose down

# 恢复备份
cp /opt/backups/control-center/control-center-YYYYMMDD_HHMMSS.db packages/control-center-backend/control-center.db

# 重新启动
docker-compose up -d
```

---

## 📞 支持

如有问题，请：
1. 查看日志: `docker-compose logs -f`
2. 检查健康状态: `curl http://localhost:3001/health`
3. 提交Issue: [GitHub Issues](https://github.com/your-repo/issues)

---

**下一步:** [Phase 1 功能开发指南](./control-center-phase1.md) 
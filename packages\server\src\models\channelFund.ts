import type {
	ChannelTransactionData,
	ChannelBalance,
	CreateChannelTransactionRequest,
	UpdateChannelTransactionRequest,
	Currency,
} from "@packages/shared";
import {
	ChannelTransactionStatus,
	ChannelTransactionType,
} from "@packages/shared";
import { AppError } from "@/core/appError.js";
import prisma, { platformPrisma } from "@/lib/prisma.js";
import type {
	Prisma,
	channel_transaction_type,
	channel_transaction_status,
	currency_type,
} from "@prisma/client";
import { APP_CONFIG } from "@/config/configManager.js";
import { findChannelNameFromConfig } from "@/config/defaultParams.js";
import { TRADING_PLATFORMS } from "@/config/defaultParams.js";
import * as ConfigService from "@/services/admin/configService.js";
import { encrypt, decrypt } from "@/utils/encryption.js";

/**
 * Prisma数据库中的通道交易记录结构
 */
interface PrismaChannelTransaction {
	transaction_id: number;
	channel_id: string;
	amount: Prisma.Decimal;
	type: channel_transaction_type;
	status: string;
	currency: string;
	remarks: string | null;
	created_at: Date | null;
	reviewed_at: Date | null;
	review_comment: string | null;
	admin_id: number | null;
}

// 使用函数获取正确的Prisma客户端
function getPrismaClient(usePlatformDb = false) {
	// 如果需要使用平台数据库且平台数据库客户端存在，则返回平台数据库客户端
	if (usePlatformDb && platformPrisma) {
		return platformPrisma;
	}
	// 否则返回默认数据库客户端
	return prisma;
}

/**
 * 转换交易记录数据，从Prisma模型转换为应用类型
 */
function transformChannelTransaction(
	transaction: PrismaChannelTransaction,
): ChannelTransactionData {
	// 直接使用数据库中存储的带符号金额，不再需要在转换时添加符号
	return {
		transaction_id: Number(transaction.transaction_id),
		channel_id: transaction.channel_id,
		amount: Number(transaction.amount), // 直接使用存储的带符号金额
		type: transaction.type as unknown as ChannelTransactionType,
		status: transaction.status as ChannelTransactionStatus,
		currency: transaction.currency as Currency,
		remarks: transaction.remarks || undefined,
		created_at:
			transaction.created_at?.toISOString() || new Date().toISOString(),
		reviewed_at:
			transaction.reviewed_at?.toISOString() || new Date().toISOString(),
		review_comment: transaction.review_comment || undefined,
		admin_id: transaction.admin_id || undefined,
	};
}

/**
 * 创建通道资金交易记录，统一在平台数据库环境执行
 * @param data 交易数据
 *   - 对于 USER_ORDER 类型，请直接传入负数金额（channel支出）
 *   - 对于 USER_EXECUTE 类型，请直接传入正数金额（channel收入）
 *   - 对于补偿交易，请直接传入与原交易相反符号的金额
 * @param client 可选的事务客户端，如果提供则在事务中执行
 */
export async function createTransaction(
	data: CreateChannelTransactionRequest & {
		status: ChannelTransactionStatus;
	},
	client?: Prisma.TransactionClient,
): Promise<ChannelTransactionData> {
	const db = client || platformPrisma;

	// 如果db为null，抛出错误
	if (!db) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	// 根据交易类型确定金额符号
	let signedAmount = data.amount;

	// 对出入金类型的交易确保其资金变动的符号表示
	if (data.type === ChannelTransactionType.WITHDRAW) {
		// 确保金额为负数
		signedAmount = -Math.abs(signedAmount);
	} else if (data.type === ChannelTransactionType.DEPOSIT) {
		// 确保金额为正数
		signedAmount = Math.abs(signedAmount);
	}
	// 对于 USER_ORDER 和 USER_EXECUTE，不再自动调整符号，由调用方保证正确性

	// 创建交易记录
	const transaction = await db.channel_transactions.create({
		data: {
			channel_id: APP_CONFIG.channelId, // 确保非空
			amount: Number(signedAmount), // 使用带符号的金额
			type: data.type as unknown as channel_transaction_type,
			status: data.status as unknown as channel_transaction_status,
			currency: data.currency as unknown as currency_type,
			remarks: data.remarks,
		},
	});

	// 如果状态为auto_confirmed，自动更新通道余额
	if (data.status === ChannelTransactionStatus.AUTO_CONFIRMED) {
		await updateChannelBalance(
			{
				channelId: APP_CONFIG.channelId,
				transactionType: data.type as string,
				amount: Number(signedAmount), // 使用带符号的金额
				currency: data.currency as string,
			},
			true,
			client, // 传递事务客户端，确保在同一事务中执行
		);
	}

	return transformChannelTransaction(transaction as PrismaChannelTransaction);
}

/**
 * 更新通道资金交易状态
 */
export async function updateTransactionStatus(
	data: UpdateChannelTransactionRequest,
	usePlatformDb: boolean,
	client?: Prisma.TransactionClient,
): Promise<ChannelTransactionData> {
	// 使用传入的事务客户端或直接使用主数据库客户端
	const db = client || (usePlatformDb ? platformPrisma : prisma);
	if (!db) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	// 查找交易记录
	const existingTransaction = await db.channel_transactions.findUnique({
		where: { transaction_id: data.transaction_id },
	});

	if (!existingTransaction) {
		throw AppError.create("NOT_FOUND", "Transaction not found");
	}

	// 检查交易状态是否可更新（只有待确认状态可以更新为已确认/已拒绝）
	if (existingTransaction.status !== ChannelTransactionStatus.PENDING) {
		throw AppError.create(
			"BAD_REQUEST",
			"Only pending transactions can be updated",
		);
	}

	// 更新交易状态
	const transaction = await db.channel_transactions.update({
		where: { transaction_id: data.transaction_id },
		data: {
			status: data.status,
			remarks: data.remarks,
			review_comment: data.review_comment,
			admin_id: data.admin_id,
			reviewed_at: new Date(),
		},
	});

	// 如果是确认交易，需要更新通道余额
	if (data.status === ChannelTransactionStatus.CONFIRMED) {
		await updateChannelBalance(
			{
				channelId: transaction.channel_id,
				transactionType: transaction.type,
				amount: Number(transaction.amount),
				currency: transaction.currency,
			},
			usePlatformDb,
			client,
		);
	}

	return transformChannelTransaction(transaction as PrismaChannelTransaction);
}

/**
 * 更新通道余额
 * @param channelId 通道ID
 * @param transactionType 交易类型
 * @param amount 金额
 * @param currency 货币类型
 * @param client 可选的事务客户端，如果提供则优先使用
 */
async function updateChannelBalance(
	{
		channelId,
		transactionType,
		amount,
		currency,
	}: {
		channelId: string;
		transactionType: string;
		amount: number;
		currency: string;
	},
	usePlatformDb: boolean,
	client?: Prisma.TransactionClient,
): Promise<void> {
	// 如果提供了客户端事务，优先使用
	const db = client || (usePlatformDb ? platformPrisma : prisma);

	if (!db) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	// 获取或创建通道余额记录
	const channelBalance = await getOrCreateChannelBalance(db, channelId);

	// 检查通道锁定状态
	const isWithdrawal = transactionType === ChannelTransactionType.WITHDRAW;
	if (channelBalance.is_locked && isWithdrawal) {
		throw AppError.create(
			"CHANNEL_LOCKED",
			"通道已被锁定，当前只能存入资金不能取出资金，请先结清欠款",
		);
	}

	// 处理余额更新
	const { newBalance, balanceField } = await calculateNewBalance(
		channelBalance,
		currency,
		amount,
	);

	// 构建更新数据
	const updateData: Record<string, unknown> = {
		[balanceField]: encrypt(newBalance.toFixed(2)),
	};

	// 处理CNY的特殊逻辑（信用额度和自动解锁）
	if (currency === "CNY") {
		await handleCNYSpecialLogic(newBalance, channelBalance, updateData);
	}

	// 更新余额
	await db.channels.update({
		where: { channel_id: channelId },
		data: updateData,
	});
}

/**
 * 获取或创建通道余额记录
 */
async function getOrCreateChannelBalance(
	db: Prisma.TransactionClient | (typeof prisma | typeof platformPrisma),
	channelId: string,
) {
	if (!db) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	let channelBalance = await db.channels.findUnique({
		where: { channel_id: channelId },
	});

	if (!channelBalance) {
		const channelName =
			findChannelNameFromConfig(channelId) || `Channel ${channelId}`;

		channelBalance = await db.channels.create({
			data: {
				channel_id: channelId,
				name: channelName,
				balance_cny: encrypt("0"),
				balance_hkd: encrypt("0"),
				balance_usd: encrypt("0"),
				is_locked: false,
			},
		});
	}

	return channelBalance;
}

/**
 * 计算新余额
 */
async function calculateNewBalance(
	channelBalance: {
		balance_cny: string;
		balance_hkd: string;
		balance_usd: string;
		[key: string]: unknown;
	},
	currency: string,
	amount: number,
): Promise<{ newBalance: number; balanceField: string }> {
	const balanceField = `balance_${currency.toLowerCase()}`;
	const currentBalance = Number(
		decrypt(channelBalance[balanceField] as string),
	);
	const newBalance = currentBalance + amount;

	// 检查非CNY货币的余额约束
	if (currency !== "CNY" && newBalance < 0) {
		throw AppError.create("INSUFFICIENT_BALANCE", `${currency}账户余额不足`);
	}

	return { newBalance, balanceField };
}

/**
 * 处理CNY的特殊业务逻辑
 */
async function handleCNYSpecialLogic(
	newBalance: number,
	channelBalance: { is_locked: boolean | null; [key: string]: unknown },
	updateData: Record<string, unknown>,
) {
	// 检查信用额度
	const businessConfig = await ConfigService.getConfig();
	const creditLimit = businessConfig.CHANNEL_CREDIT_LIMIT;
	if (newBalance < -creditLimit) {
		throw AppError.create(
			"CHANNEL_BALANCE_INSUFFICIENT",
			`操作将超出预授权信用额度（${creditLimit} CNY），请先充值`,
		);
	}

	// 自动解锁逻辑
	if (channelBalance.is_locked && newBalance >= 0) {
		updateData.is_locked = false;
	}
}

/**
 * 获取通道余额，仅限通道发起
 */
export async function getChannelBalance(): Promise<ChannelBalance> {
	if (!platformPrisma) {
		throw AppError.create("NOT_FOUND", "Platform database not found");
	}

	const channelId = APP_CONFIG.channelId;

	// 获取通道当前余额
	let channelBalance = await platformPrisma.channels.findUnique({
		where: {
			channel_id: channelId,
		},
	});

	// 如果通道余额不存在，则创建初始余额记录
	if (!channelBalance) {
		// 从配置中查找通道名称
		const channelName =
			findChannelNameFromConfig(channelId) || `Channel ${channelId}`;

		channelBalance = await platformPrisma.channels.create({
			data: {
				channel_id: channelId,
				name: channelName,
				balance_cny: encrypt("0"),
				balance_hkd: encrypt("0"),
				balance_usd: encrypt("0"),
				is_locked: false,
			},
		});
	}

	return {
		channel_id: channelBalance.channel_id,
		balance_cny: Number(decrypt(channelBalance.balance_cny)),
		balance_hkd: Number(decrypt(channelBalance.balance_hkd)),
		balance_usd: Number(decrypt(channelBalance.balance_usd)),
		is_locked: Boolean(channelBalance.is_locked),
	};
}

/**
 * 获取所有通道余额
 */
export async function getAllChannelBalances(): Promise<ChannelBalance[]> {
	// 获取数据库中现有的通道记录
	const existingChannels = await prisma.channels.findMany();
	const existingChannelIds = new Set(
		existingChannels.map((ch) => ch.channel_id),
	);

	// 获取当前平台ID
	const platformId = APP_CONFIG.tradingPlatformId;
	if (platformId && platformId in TRADING_PLATFORMS) {
		const platform =
			TRADING_PLATFORMS[platformId as keyof typeof TRADING_PLATFORMS];
		if (platform.channels) {
			// 创建不存在于数据库中的通道记录
			const createPromises: Promise<unknown>[] = [];

			// 遍历当前平台的通道
			for (const channelId in platform.channels) {
				// 如果通道不存在于数据库中，则创建初始记录
				if (!existingChannelIds.has(channelId)) {
					const channel =
						platform.channels[channelId as keyof typeof platform.channels];
					if (channel) {
						createPromises.push(
							prisma.channels.create({
								data: {
									channel_id: channelId,
									name: channel.name,
									balance_cny: encrypt("0"),
									balance_hkd: encrypt("0"),
									balance_usd: encrypt("0"),
									is_locked: false,
								},
							}),
						);
					}
				}
			}

			// 执行所有创建操作（如果有的话）
			if (createPromises.length > 0) {
				await Promise.all(createPromises);
				// 重新获取所有通道记录
				return (await prisma.channels.findMany()).map((balance) => ({
					channel_id: balance.channel_id,
					balance_cny: Number(decrypt(balance.balance_cny)),
					balance_hkd: Number(decrypt(balance.balance_hkd)),
					balance_usd: Number(decrypt(balance.balance_usd)),
					is_locked: Boolean(balance.is_locked),
				}));
			}
		}
	}

	// 直接返回现有记录
	return existingChannels.map((balance) => ({
		channel_id: balance.channel_id,
		balance_cny: Number(decrypt(balance.balance_cny)),
		balance_hkd: Number(decrypt(balance.balance_hkd)),
		balance_usd: Number(decrypt(balance.balance_usd)),
		is_locked: Boolean(balance.is_locked),
	}));
}

/**
 * 获取通道交易记录，多可选参数，非末位不要省略以免混淆
 */
export async function getChannelTransactions(
	limit = 50,
	offset = 0,
	status?: ChannelTransactionStatus,
	usePlatformDb = false,
	channelId?: string,
): Promise<ChannelTransactionData[]> {
	const db = getPrismaClient(usePlatformDb);
	const whereClause: Record<string, unknown> = {
		channel_id: channelId || APP_CONFIG.channelId,
	};

	// 如果提供了状态参数，添加到查询条件中
	if (status) {
		whereClause.status = status;
	}

	const transactions = await db.channel_transactions.findMany({
		where: whereClause,
		orderBy: { created_at: "desc" },
		take: limit,
		skip: offset,
	});

	return transactions.map((transaction) =>
		transformChannelTransaction(transaction as PrismaChannelTransaction),
	);
}

/**
 * 获取单条出入金记录
 */
export async function getTransactionById(
	transactionId: string,
	usePlatformDb = false,
): Promise<ChannelTransactionData | null> {
	const db = getPrismaClient(usePlatformDb);
	const transaction = await db.channel_transactions.findUnique({
		where: { transaction_id: Number(transactionId) },
	});

	if (!transaction) return null;

	return transformChannelTransaction(transaction as PrismaChannelTransaction);
}

/**
 * 获取所有通道
 */
export async function getAllChannels(): Promise<ChannelBalance[]> {
	const channels = await prisma.channels.findMany();

	return channels.map((channel) => ({
		...channel,
		channel_id: channel.channel_id,
		balance_cny: Number(decrypt(channel.balance_cny)),
		balance_hkd: Number(decrypt(channel.balance_hkd)),
		balance_usd: Number(decrypt(channel.balance_usd)),
		is_locked: Boolean(channel.is_locked),
	}));
}

export async function getPendingTransactions(limit = 50, offset = 0) {
	const transactions = await prisma.channel_transactions.findMany({
		where: { status: "pending" },
		take: limit,
		skip: offset,
		orderBy: { created_at: "desc" },
	});

	const formattedTransactions = transactions.map((transaction) =>
		transformChannelTransaction(transaction as PrismaChannelTransaction),
	);

	const total = await prisma.channel_transactions.count({
		where: { status: "pending" },
	});

	return { transactions: formattedTransactions, total };
}

/**
 * 获取通道交易记录总数
 */
export async function getChannelTransactionsCount(
	status?: ChannelTransactionStatus,
	usePlatformDb = false,
	channelId?: string,
): Promise<number> {
	const db = getPrismaClient(usePlatformDb);

	const whereClause: Record<string, unknown> = {
		channel_id: channelId || APP_CONFIG.channelId,
	};

	// 如果提供了状态参数，添加到查询条件中
	if (status) {
		whereClause.status = status;
	}

	return db.channel_transactions.count({
		where: whereClause,
	});
}

export async function getPendingTransactionsCount() {
	if (!platformPrisma) {
		throw AppError.create("NOT_FOUND", "Platform database not found");
	}

	return platformPrisma.channel_transactions.count({
		where: { status: "pending" },
	});
}

/**
 * 获取所有通道的交易记录
 */
export async function getAllTransactions(
	limit = 50,
	offset = 0,
	status?: ChannelTransactionStatus,
): Promise<ChannelTransactionData[]> {
	const whereClause: Record<string, unknown> = {};

	// 如果提供了状态参数，添加到查询条件中
	if (status) {
		whereClause.status = status;
	}

	const transactions = await prisma.channel_transactions.findMany({
		where: whereClause,
		orderBy: { created_at: "desc" },
		take: limit,
		skip: offset,
	});

	return transactions.map((transaction) =>
		transformChannelTransaction(transaction as PrismaChannelTransaction),
	);
}

/**
 * 获取所有通道交易记录总数
 */
export async function getAllTransactionsCount(
	status?: ChannelTransactionStatus,
): Promise<number> {
	const whereClause: Record<string, unknown> = {};

	// 如果提供了状态参数，添加到查询条件中
	if (status) {
		whereClause.status = status;
	}

	return prisma.channel_transactions.count({
		where: whereClause,
	});
}

/**
 * 检查并锁定负余额通道，仅限平台使用
 * 此函数应在每日零点执行，检查所有通道余额，如果有负余额则锁定通道
 */
export async function checkAndLockNegativeBalances(): Promise<{
	locked: string[];
	notLocked: string[];
}> {
	const allChannels = await prisma.channels.findMany();

	const lockedChannels: string[] = [];
	const notLockedChannels: string[] = [];

	// 检查并更新每个通道状态
	for (const channel of allChannels) {
		// 只检查人民币余额是否为负
		const hasNegativeBalance = Number(decrypt(channel.balance_cny)) < 0;

		// 如果有负余额且未锁定，则锁定
		if (hasNegativeBalance && !channel.is_locked) {
			try {
				// 锁定通道
				await prisma.channels.update({
					where: { channel_id: channel.channel_id },
					data: { is_locked: true },
				});

				lockedChannels.push(channel.channel_id);
			} catch (error) {
				// 记录错误但继续执行
				console.error(`锁定通道 ${channel.channel_id} 失败:`, error);
			}
		} else if (hasNegativeBalance) {
			// 已经锁定的通道
			lockedChannels.push(channel.channel_id);
		} else {
			notLockedChannels.push(channel.channel_id);
		}
	}

	return { locked: lockedChannels, notLocked: notLockedChannels };
}

/**
 * 仅更新通道交易记录的备注信息，不改变交易状态
 * @param transactionId 交易ID
 * @param remarks 新的备注内容
 * @param usePlatformDb 是否使用平台数据库
 * @returns 更新后的交易记录
 */
export async function updateChannelTransactionRemarks(
	transactionId: number,
	remarks: string,
	usePlatformDb: boolean,
): Promise<ChannelTransactionData> {
	const db = usePlatformDb ? platformPrisma : prisma;
	if (!db) {
		throw AppError.create("NOT_FOUND", "Database client not available");
	}

	// 查找交易记录
	const existingTransaction = await db.channel_transactions.findUnique({
		where: { transaction_id: transactionId },
	});

	if (!existingTransaction) {
		throw AppError.create("NOT_FOUND", "Transaction not found");
	}

	// 仅更新备注信息
	const transaction = await db.channel_transactions.update({
		where: { transaction_id: transactionId },
		data: { remarks: remarks },
	});

	return transformChannelTransaction(transaction as PrismaChannelTransaction);
}

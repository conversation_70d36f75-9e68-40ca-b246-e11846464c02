<script lang="ts">
  import { onMount } from "svelte";
  import { authStore } from "$stores/auth";
  import { socketStore } from "$stores/socket";
  import Header from "$components/Header.svelte";
  import Sidebar from "$components/Sidebar.svelte";
  import Dashboard from "./routes/+page.svelte";

  let sidebarOpen = false;

  onMount(() => {
    // 初始化认证状态
    authStore.init();

    // 如果已登录，初始化WebSocket连接
    authStore.subscribe((auth) => {
      if (auth.isAuthenticated) {
        socketStore.connect();
      } else {
        socketStore.disconnect();
      }
    });
  });

  function toggleSidebar() {
    sidebarOpen = !sidebarOpen;
  }
</script>

<svelte:head>
  <title>总控端 - INK Control Center</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <Header {toggleSidebar} />

  <div class="flex">
    <Sidebar bind:open={sidebarOpen} />

    <main
      class="flex-1 p-6 transition-all duration-300"
      class:ml-64={sidebarOpen}
      class:ml-0={!sidebarOpen}
    >
      <div class="max-w-7xl mx-auto">
        <Dashboard />
      </div>
    </main>
  </div>
</div>

<style>
  :global(html) {
    scroll-behavior: smooth;
  }

  :global(body) {
    font-family:
      "Inter",
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      Roboto,
      sans-serif;
  }
</style>

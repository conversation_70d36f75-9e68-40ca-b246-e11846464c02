/**
 * Date utilities for consistent date handling throughout the application
 *
 * The system uses the following date formats:
 * - Date objects internally for all date operations
 * - YYYY-MM-DD for database interactions (ISO format)
 * - YYYYMMDD for API interactions (compact format without separators)
 */

/**
 * Converts a Date object to YYYYMMDD format string (compact format)
 * @param date Date object to convert
 * @returns YYYYMMDD format string
 */
export function dateToCompactString(date: Date): string {
	return date.toISOString().slice(0, 10).replace(/-/g, "");
}

/**
 * Converts a Date object to YYYY-MM-DD format string (ISO format)
 * @param date Date object to convert
 * @returns YYYY-MM-DD format string
 */
export function dateToISOString(date: Date): string {
	return date.toISOString().slice(0, 10);
}

/**
 * Converts a YYYYMMDD format string to a Date object
 * @param dateStr String in YYYYMMDD format
 * @returns Date object
 */
export function compactStringToDate(dateStr: string): Date {
	if (!dateStr || dateStr.length !== 8) {
		throw new Error(
			`Invalid date format: ${dateStr}. Expected YYYYMMDD format.`,
		);
	}

	const year = Number.parseInt(dateStr.substring(0, 4), 10);
	// Month in Date is 0-indexed (0 = January)
	const month = Number.parseInt(dateStr.substring(4, 6), 10) - 1;
	const day = Number.parseInt(dateStr.substring(6, 8), 10);

	const date = new Date(year, month, day);

	// Validate the date is correct
	if (
		date.getFullYear() !== year ||
		date.getMonth() !== month ||
		date.getDate() !== day
	) {
		throw new Error(`Invalid date: ${dateStr}`);
	}

	return date;
}

/**
 * Converts a YYYY-MM-DD format string to a Date object
 * @param dateStr String in YYYY-MM-DD format
 * @returns Date object
 */
export function isoStringToDate(dateStr: string): Date {
	if (!dateStr || !/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
		throw new Error(
			`Invalid date format: ${dateStr}. Expected YYYY-MM-DD format.`,
		);
	}

	// Create date and ensure time is set to midnight UTC to avoid timezone issues
	const date = new Date(`${dateStr}T00:00:00.000Z`);

	// Validate the date is correct
	if (Number.isNaN(date.getTime())) {
		throw new Error(`Invalid date: ${dateStr}`);
	}

	return date;
}

/**
 * Converts a compact string YYYYMMDD to ISO string YYYY-MM-DD
 * @param compactStr String in YYYYMMDD format
 * @returns String in YYYY-MM-DD format
 */
export function compactToISOString(compactStr: string): string {
	if (!compactStr || compactStr.length !== 8) {
		throw new Error(
			`Invalid date format: ${compactStr}. Expected YYYYMMDD format.`,
		);
	}

	return `${compactStr.substring(0, 4)}-${compactStr.substring(4, 6)}-${compactStr.substring(6, 8)}`;
}

/**
 * Converts an ISO string YYYY-MM-DD to compact string YYYYMMDD
 * @param isoStr String in YYYY-MM-DD format
 * @returns String in YYYYMMDD format
 */
export function isoToCompactString(isoStr: string): string {
	if (!isoStr || !/^\d{4}-\d{2}-\d{2}$/.test(isoStr)) {
		throw new Error(
			`Invalid date format: ${isoStr}. Expected YYYY-MM-DD format.`,
		);
	}

	return isoStr.replace(/-/g, "");
}

/**
 * Get the current date in China timezone (UTC+8)
 * @returns Date object representing the current date in China timezone
 */
export function getChinaDate(): Date {
	// Add 8 hours to UTC to get China time
	return new Date(new Date().getTime() + 8 * 60 * 60 * 1000);
}

/**
 * Get the current date in China timezone as a YYYYMMDD string
 * @returns YYYYMMDD format string for the current date in China timezone
 */
export function getChinaDateCompactString(): string {
	return dateToCompactString(getChinaDate());
}

/**
 * Get the current date in China timezone as a YYYY-MM-DD string
 * @returns YYYY-MM-DD format string for the current date in China timezone
 */
export function getChinaDateISOString(): string {
	return dateToISOString(getChinaDate());
}

/**
 * Get the current date and time in China timezone as a full ISO string
 * @returns YYYY-MM-DDTHH:mm:ss.sssZ format string for the current date and time in China timezone
 */
export function getChinaDateTimeISOString(): string {
	return getChinaDate().toISOString();
}

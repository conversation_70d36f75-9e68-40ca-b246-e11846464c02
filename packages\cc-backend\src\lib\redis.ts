import Redis from "ioredis";
import { logger } from "../utils/logger";

// 创建Redis客户端
export const redisClient = new Redis({
	host: process.env.REDIS_HOST || "localhost",
	port: Number.parseInt(process.env.REDIS_PORT || "6379"),
	password: process.env.REDIS_PASSWORD,
	db: Number.parseInt(process.env.REDIS_DB || "0"),
	maxRetriesPerRequest: 3,
	retryDelayOnFailover: 100,
	lazyConnect: true,
});

// 连接事件监听
redisClient.on("connect", () => {
	logger.info("✅ Redis 连接成功");
});

redisClient.on("error", (error) => {
	logger.error("❌ Redis 连接错误:", error);
});

redisClient.on("close", () => {
	logger.info("🔌 Redis 连接已关闭");
});

// 导出 Redis 实例
export default redisClient;

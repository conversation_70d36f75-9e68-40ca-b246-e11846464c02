import { Request, Response, NextFunction } from "express";
import { logger } from "../utils/logger";

export interface CustomError extends Error {
	statusCode?: number;
	status?: string;
}

export const errorHandler = (
	err: CustomError,
	req: Request,
	res: Response,
	next: NextFunction,
) => {
	let error = { ...err };
	error.message = err.message;

	// 记录错误日志
	logger.error("错误详情:", {
		message: err.message,
		stack: err.stack,
		url: req.url,
		method: req.method,
		ip: req.ip,
		userAgent: req.get("User-Agent"),
	});

	// Prisma 错误处理
	if (err.name === "PrismaClientKnownRequestError") {
		const message = "数据库操作错误";
		error = { ...error, message, statusCode: 400 };
	}

	// JWT 错误处理
	if (err.name === "JsonWebTokenError") {
		const message = "无效的访问令牌";
		error = { ...error, message, statusCode: 401 };
	}

	if (err.name === "TokenExpiredError") {
		const message = "访问令牌已过期";
		error = { ...error, message, statusCode: 401 };
	}

	// 验证错误处理
	if (err.name === "ValidationError") {
		const message = "数据验证失败";
		error = { ...error, message, statusCode: 400 };
	}

	// 默认错误
	const statusCode = error.statusCode || 500;
	const message = error.message || "服务器内部错误";

	res.status(statusCode).json({
		success: false,
		error: message,
		...(process.env.NODE_ENV === "development" && { stack: err.stack }),
	});
};

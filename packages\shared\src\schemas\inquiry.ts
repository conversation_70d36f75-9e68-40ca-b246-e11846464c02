import { z } from "zod";
import {
	INQUIRY_EXPIRATION_TIME_IN_SECONDS,
	StructureValue,
} from "../types/inquiry";

// 辅助函数：验证两位小数的正数
const positiveDecimal = z
	.number()
	.positive()
	.multipleOf(0.01)
	.transform((n) => Number(n.toFixed(2)));

// 股票代码验证
export const tsCodeSchema = z
	.string()
	.min(1)
	.regex(/^\d{6}\.(SH|SZ|BJ)$/, "股票代码格式不正确");

// 基础类型
export const stockInfoSchema = z.object({
	ts_code: z.string().min(1),
	name: z.string().min(1),
});

// 单个询价请求验证
export const quoteRequestSchema = z.object({
	ts_code: z.string().min(1),
	scale: positiveDecimal, // 名义本金必须是正数且两位小数
	structure: z.enum(Object.keys(StructureValue) as [string, ...string[]]), // 使用类型系统确保枚举值正确
	term: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(14)]), // 期限只允许 1, 2, 3 (月) 或 14 (天)
});

// 批量询价请求验证
export const batchQuoteRequestSchema = z.object({
	quotes: z.array(quoteRequestSchema).min(1), // 至少需要一个询价请求
});

// 搜索请求验证
export const searchQuerySchema = z.object({
	query: z.string().optional(),
	offset: z
		.string()
		.transform((val) => Number.parseInt(val))
		.pipe(z.number().int().min(0))
		.optional(),
});

// 导出类型
export type QuoteRequestSchemaType = z.infer<typeof quoteRequestSchema>;
export type BatchQuoteRequestSchemaType = z.infer<
	typeof batchQuoteRequestSchema
>;

// 使用示例：
// const validateQuoteRequest = (data: unknown) => {
//   return quoteRequestSchema.parse(data)
// }

// 缓存的报价信息验证
export const cachedQuoteSchema = z.object({
	quote: positiveDecimal, // 报价必须是两位小数的正数
	structure: z.enum(Object.keys(StructureValue) as [string, ...string[]]),
	term: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(14)]), // 期限只允许 1, 2, 3 (月) 或 14 (天)
	timestamp: z
		.number()
		.int()
		.min(Date.now() - INQUIRY_EXPIRATION_TIME_IN_SECONDS * 1000), // 确保时间戳在有效期内
});

export type CachedQuote = z.infer<typeof cachedQuoteSchema>;

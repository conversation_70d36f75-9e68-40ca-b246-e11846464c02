// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String
  role      UserRole @default(OPERATOR)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  deployments Deployment[]
  operations  Operation[]

  @@map("users")
}

// 服务器配置表
model Server {
  id          String       @id @default(cuid())
  name        String
  host        String
  port        Int          @default(22)
  username    String
  privateKey  String? // SSH私钥，加密存储
  password    String? // SSH密码，加密存储
  description String?
  environment String       @default("production") // development, staging, production
  status      ServerStatus @default(UNKNOWN)
  lastCheckAt DateTime?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // 关联关系
  deployments    Deployment[]
  monitoringData MonitoringData[]

  @@map("servers")
}

// 部署记录表
model Deployment {
  id          String           @id @default(cuid())
  serverId    String
  userId      String
  projectName String
  branch      String
  services    String // JSON array of services to deploy
  envVars     String // JSON object of environment variables
  status      DeploymentStatus @default(PENDING)
  startedAt   DateTime         @default(now())
  completedAt DateTime?
  logs        String? // 部署日志
  error       String? // 错误信息

  // 关联关系
  server Server @relation(fields: [serverId], references: [id], onDelete: Cascade)
  user   User   @relation(fields: [userId], references: [id])

  @@map("deployments")
}

// 监控数据表
model MonitoringData {
  id        String   @id @default(cuid())
  serverId  String
  timestamp DateTime @default(now())

  // 系统指标
  cpuUsage    Float?
  memoryUsage Float?
  diskUsage   Float?
  networkIO   String? // JSON object

  // 应用指标
  services String? // JSON array of service statuses

  // 数据库指标
  dbConnections Int?
  dbQueries     Float?
  dbSlowQueries Int?

  // 关联关系
  server Server @relation(fields: [serverId], references: [id], onDelete: Cascade)

  @@map("monitoring_data")
}

// 操作记录表
model Operation {
  id          String          @id @default(cuid())
  userId      String
  type        OperationType
  target      String // 操作目标（服务器ID、服务名称等）
  action      String // 具体操作
  payload     String? // 操作参数，JSON格式
  result      String? // 操作结果
  status      OperationStatus @default(PENDING)
  executedAt  DateTime        @default(now())
  completedAt DateTime?
  error       String? // 错误信息

  // 关联关系
  user User @relation(fields: [userId], references: [id])

  @@map("operations")
}

// 系统配置表
model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  updatedAt   DateTime @updatedAt

  @@map("system_config")
}

// 枚举定义
enum UserRole {
  ADMIN // 管理员
  OPERATOR // 操作员
  VIEWER // 只读用户
}

enum ServerStatus {
  ONLINE // 在线
  OFFLINE // 离线
  ERROR // 错误
  UNKNOWN // 未知
}

enum DeploymentStatus {
  PENDING // 等待中
  RUNNING // 运行中
  SUCCESS // 成功
  FAILED // 失败
  CANCELLED // 已取消
}

enum OperationType {
  DEPLOYMENT // 部署操作
  MONITORING // 监控操作
  PROXY // 代理操作
  SYSTEM // 系统操作
}

enum OperationStatus {
  PENDING // 等待中
  RUNNING // 运行中
  SUCCESS // 成功
  FAILED // 失败
}

# 服务配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173

# 数据库配置
DATABASE_URL="file:./dev.db"

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# 日志配置
LOG_LEVEL=info

# SSH配置（用于远程服务器操作）
DEFAULT_SSH_USERNAME=root
DEFAULT_SSH_PORT=22

# 部署配置
DEFAULT_PROJECT_PATH=/opt/ink-dev
DEFAULT_DOCKER_COMPOSE_FILE=docker-compose.yml

# 监控配置
MONITORING_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads 
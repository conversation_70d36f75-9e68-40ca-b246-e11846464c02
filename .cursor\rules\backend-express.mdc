---
description: 
globs: packages/server/src/**
alwaysApply: false
---
## Backend Conventions (Node.js/TypeScript/Express)

*   **Language:** Use TypeScript for all backend code.
*   **Framework:** Structure the application using Express. Define routes in the `src/routes` directory.
*   **Routing:** Use the `wrapUserRoute` utility (`@/utils/routeWrapper.js`) for defining authenticated API routes to ensure consistent handling of requests, responses, and errors.
*   **Architecture:**
    *   Place route definitions in `src/routes`.
    *   Encapsulate business logic within services in `src/services`.
    *   Handle database interactions via models in `src/models` (interacting with Prisma).
    *   Use middleware defined in `src/middlewares` for concerns like authentication, validation, etc. (often applied via `wrapUserRoute` or in `app.ts`).
*   **Error Handling:** Use the custom `AppError` class (`@/core/appError.js`) for throwing structured application errors. Catch errors appropriately and allow the global error handler (likely configured in `app.ts` or via `wrapUserRoute`) to format the response.
*   **Database:** Interact with the database using Prisma. Define the schema in `packages/server/prisma/schema.prisma`. Use Prisma Client via the models layer.
*   **Logging:** Use the shared logger instance imported from `@/utils/logger.ts`. Create child loggers for specific modules using `getChildLogger('moduleName')`. Follow Pino's recommended logging practices (e.g., `logger.info({ context }, "message")`).
*   **Async Operations:** Use `async/await` for all asynchronous operations (DB calls, API calls, etc.).
*   **Queues:** Interact with queues (likely Redis/BullMQ) via the modules defined in `src/queue` for background tasks.
*   **Configuration:** Access configuration values via the `configManager` (`@/config/configManager.js`).
*   **Types:** Import shared types from `@packages/shared`.


import { Router } from "express";
import { wrapAdminRoute } from "@/utils/routeWrapper.js";
import path from "node:path";
import fs from "node:fs/promises";
import { isTradingPlatform, ENV } from "@/config/configManager.js";
import { AppError } from "@/core/appError.js";
import logger from "@/utils/logger.js";
import multer from "multer";
import { v4 as uuidv4 } from "uuid";
import * as sharedConfigService from "@/services/admin/sharedConfigService.js";

const router = Router();

// 使用与 env.ts 相同的逻辑确定目录位置
const cwd = process.cwd();
const isInRootDir = !cwd.endsWith("server");
const PUBLIC_DIR = isInRootDir
	? path.join(cwd, "packages", "server", "public")
	: path.join(cwd, "public");

// 自定义模板存储目录
const UPLOAD_DIR = ENV.UPLOAD_DIR || "/var/uploads";
const CUSTOM_TEMPLATES_DIR = path.join(UPLOAD_DIR, "custom-templates");

// 确保自定义模板目录存在
try {
	fs.mkdir(CUSTOM_TEMPLATES_DIR, { recursive: true });
} catch (error) {
	logger.error(error, "无法创建自定义模板目录");
}

// 配置文件上传
const storage = multer.diskStorage({
	destination: (_req, _file, cb) => {
		cb(null, CUSTOM_TEMPLATES_DIR);
	},
	filename: (_req, file, cb) => {
		const uniqueSuffix = `${uuidv4()}-${Date.now()}${path.extname(file.originalname)}`;
		cb(null, uniqueSuffix);
	},
});

const upload = multer({
	storage,
	limits: {
		fileSize: 10 * 1024 * 1024, // 限制上传大小为10MB
	},
});

// 获取ISDA主协议模板
router.get(
	"/isda-master",
	wrapAdminRoute(async (req, res) => {
		try {
			// 先尝试获取自定义模板
			const config = await sharedConfigService.getSharedConfig();
			const agreementTemplates = config.agreement_templates;
			const uid = agreementTemplates?.["isda-master"]?.filter?.uid;

			if (uid) {
				const customTemplatePath = path.join(CUSTOM_TEMPLATES_DIR, uid);

				// 检查自定义模板文件是否存在
				try {
					await fs.access(customTemplatePath);
					// 存在则返回自定义模板，使用root属性避免在windows系统下sendFile报错参数不是绝对路径
					return res.sendFile(uid, { root: CUSTOM_TEMPLATES_DIR });
				} catch (err) {
					// 自定义模板不存在，降级到默认模板
					logger.warn("自定义ISDA主协议模板文件不存在，使用默认模板");
				}
			}

			// 使用默认模板
			const templatePath = path.join(
				PUBLIC_DIR,
				"templates/ISDA主协议及附约-中英.pdf",
			);

			// 检查文件是否存在
			await fs.access(templatePath);

			// 返回文件
			res.sendFile(templatePath);
		} catch (error) {
			logger.error(error, "获取ISDA主协议模板失败");
			throw AppError.create("NOT_FOUND", "模板文件不存在");
		}
	}),
);

// 获取ISDA补充协议模板
router.get(
	"/isda-supplement",
	wrapAdminRoute(async (req, res) => {
		try {
			// 先尝试获取自定义模板
			const config = await sharedConfigService.getSharedConfig();
			const agreementTemplates = config.agreement_templates;
			const uid = agreementTemplates?.["isda-supplement"]?.filter?.uid;

			if (uid) {
				const customTemplatePath = path.join(CUSTOM_TEMPLATES_DIR, uid);

				// 检查自定义模板文件是否存在
				try {
					await fs.access(customTemplatePath);
					// 存在则返回自定义模板
					return res.sendFile(uid, { root: CUSTOM_TEMPLATES_DIR });
				} catch (err) {
					// 自定义模板不存在，降级到默认模板
					logger.warn("自定义ISDA补充协议模板文件不存在，使用默认模板");
				}
			}

			// 使用默认模板
			const templatePath = path.join(
				PUBLIC_DIR,
				"templates/有关于2002主协议的补充协议-中英-v2.pdf",
			);

			// 检查文件是否存在
			await fs.access(templatePath);

			// 返回文件
			res.sendFile(templatePath);
		} catch (error) {
			logger.error(error, "获取ISDA补充协议模板失败");
			throw AppError.create("NOT_FOUND", "模板文件不存在");
		}
	}),
);

// 上传自定义协议模板
router.post(
	"/upload",
	upload.single("file"),
	wrapAdminRoute<{ originalName?: string }>(async (req, res) => {
		// 检查是否为交易台版本
		if (!isTradingPlatform()) {
			throw AppError.create("FORBIDDEN", "无权限访问");
		}

		if (!req.file) {
			throw AppError.create("BAD_REQUEST", "未提供文件");
		}

		const file = req.file;
		if (file.mimetype !== "application/pdf") {
			// 删除非PDF文件
			await fs.unlink(file.path);
			throw AppError.create("BAD_REQUEST", "只支持PDF格式的文件");
		}

		res.status(200).json({
			message: "模板上传成功",
			uid: file.filename,
			name: req.body.originalName || file.originalname,
		});
	}),
);

// 获取当前模板配置
router.get(
	"/config",
	wrapAdminRoute(async (_req, res) => {
		const config = await sharedConfigService.getSharedConfig();
		const templates = config.agreement_templates;

		res.status(200).json(templates);
	}),
);

// 更新模板配置
router.post(
	"/config",
	wrapAdminRoute<
		Record<
			string,
			{
				filter: { uid: string; name: string };
				stamp: [number, number, number][];
				signature: [number, number, number][];
			}
		>
	>(async (req, res) => {
		// 检查是否为交易台版本
		if (!isTradingPlatform()) {
			throw AppError.create("FORBIDDEN", "无权限访问");
		}

		const newAgreementTemplates = req.body;
		const result = await sharedConfigService.updateSharedConfig(
			{ agreement_templates: newAgreementTemplates },
			req.jwt.admin_id,
		);
		res.status(200).json(result);
	}),
);

// 获取指定自定义模板
router.get(
	"/custom/:uid",
	wrapAdminRoute(async (req, res) => {
		// 检查是否为交易台版本
		if (!isTradingPlatform()) {
			throw AppError.create("FORBIDDEN", "无权限访问");
		}

		const { uid } = req.params;
		if (!uid) {
			throw AppError.create("BAD_REQUEST", "未指定模板UID");
		}

		try {
			const templatePath = path.join(CUSTOM_TEMPLATES_DIR, uid);
			await fs.access(templatePath);
			res.sendFile(templatePath);
		} catch (error) {
			logger.error(error, `获取自定义模板 ${uid} 失败`);
			throw AppError.create("NOT_FOUND", "指定的模板文件不存在");
		}
	}),
);

export default router;

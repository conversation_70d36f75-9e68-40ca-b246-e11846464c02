generator client {
  provider = "prisma-client-js"
  output   = "../../../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model admins {
  admin_id              Int                     @id @default(autoincrement())
  username              String                  @unique @db.VarChar(32)
  password_hash         String                  @db.VarChar(60)
  name                  String                  @db.VarChar(32)
  is_active             Boolean?                @default(true)
  created_at            DateTime?               @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  permissions           admin_permission[]      @default([basic])
  assets                assets[]
  audits                audits[]
  bank_accounts         bank_accounts[]
  business_configs      business_configs[]
  order_modifications   order_modifications[]
  platform_configs      platform_configs[]
  shared_configs        shared_configs[]
  site_configs          site_configs[]
  system_status_history system_status_history[]
}

model audit_sequences {
  date_str            String @id @db.VarChar(8)
  qualification_count Int    @default(0) @db.SmallInt
  fund_count          Int    @default(0) @db.SmallInt
}

model audits {
  audit_id   Int            @id @default(autoincrement())
  user_id    Int
  type       audit_type
  status     audit_status   @default(pending)
  admin_id   Int?
  created_at DateTime?      @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  comment    String?
  data       Json           @default("{}")
  operation  String?        @db.VarChar(32)
  amount     Decimal?       @db.Decimal(10, 2)
  currency   currency_type?
  admins     admins?        @relation(fields: [admin_id], references: [admin_id], onDelete: NoAction, onUpdate: NoAction)
  users      users          @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)

  @@index([user_id], map: "idx_audits_user")
}

model business_configs {
  config_id  Int       @id @default(autoincrement())
  config     Json
  created_at DateTime? @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  admin_id   Int?
  admins     admins?   @relation(fields: [admin_id], references: [admin_id], onDelete: NoAction, onUpdate: NoAction)
}

model inquiries {
  inquiry_id      Int            @id @default(autoincrement())
  user_id         Int
  ts_code         String         @db.VarChar(9)
  scale           Int            @db.SmallInt
  term            Int            @db.SmallInt
  structure       String         @db.VarChar(8)
  quote           Decimal        @db.Decimal(6, 2)
  status          inquiry_status
  created_at      DateTime?      @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  external_quotes Json?          @default("{}")
  quote_diffs     Json?          @default("{}")
  users           users          @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model notification_reads {
  notif_id      Int
  user_id       Int
  created_at    DateTime?     @default(now()) @db.Timestamptz(6)
  notifications notifications @relation(fields: [notif_id], references: [notif_id], onDelete: Cascade, onUpdate: NoAction)
  users         users         @relation(fields: [user_id], references: [user_id], onDelete: Cascade, onUpdate: NoAction)

  @@id([notif_id, user_id])
  @@index([user_id, notif_id], map: "idx_notification_reads_user")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model notifications {
  notif_id           Int                  @id @default(autoincrement())
  title              String               @db.VarChar(64)
  content            String
  type               notification_type
  target_type        notification_target
  user_id            Int?
  metadata           Json?                @default("{}")
  created_at         DateTime?            @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  notification_reads notification_reads[]
  users              users?               @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model order_sequences {
  date_str      String @id @db.VarChar(8)
  last_sequence Int    @db.SmallInt
}

model orders {
  trade_no              String                @id @db.VarChar(20)
  user_id               Int
  ts_code               String                @db.VarChar(9)
  entry_price           Decimal               @db.Decimal(6, 2)
  exercise_price        Decimal               @db.Decimal(10, 6)
  settle_price          Decimal               @db.Decimal(6, 2)
  scale                 Int                   @db.SmallInt
  total_scale           Int                   @db.SmallInt
  term                  Int                   @db.SmallInt
  quote                 Decimal               @db.Decimal(6, 2)
  status                order_status
  created_at            DateTime?             @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  closed_at             DateTime?             @db.Timestamptz(6)
  is_split              Boolean               @default(false)
  structure             String                @db.VarChar(8)
  expiry_date           DateTime?             @db.Timestamptz(6)
  expiry_date_confirmed Boolean?              @default(false)
  quote_provider        String?               @db.VarChar(32)
  quote_diff            Decimal?              @db.Decimal(6, 2)
  order_modifications   order_modifications[]
  users                 users                 @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
  pending_orders        pending_orders[]
  positions             positions?
  transactions          transactions[]
}

model pending_orders {
  pending_id            Int          @id @default(autoincrement())
  trade_no              String?      @db.VarChar(20)
  user_id               Int
  ts_code               String       @db.VarChar(9)
  entry_price           Decimal      @db.Decimal(6, 2)
  exercise_price        Decimal      @db.Decimal(10, 6)
  settle_price          Decimal      @db.Decimal(6, 2)
  scale                 Int          @db.SmallInt
  total_scale           Int          @db.SmallInt
  term                  Int          @db.SmallInt
  quote                 Decimal      @db.Decimal(6, 2)
  status                order_status
  created_at            DateTime?    @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  closed_at             DateTime?    @db.Timestamptz(6)
  is_split              Boolean      @default(false)
  limit_price           Decimal?     @db.Decimal(6, 2)
  structure             String?      @db.VarChar(8)
  expiry_date           DateTime?    @db.Timestamptz(6)
  expiry_date_confirmed Boolean?     @default(false)
  quote_provider        String?      @db.VarChar(32)
  quote_diff            Decimal?     @db.Decimal(6, 2)
  orders                orders?      @relation(fields: [trade_no], references: [trade_no], onDelete: NoAction, onUpdate: NoAction)
  users                 users        @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model positions {
  trade_no              String    @id @db.VarChar(20)
  user_id               Int
  ts_code               String    @db.VarChar(9)
  entry_price           Decimal   @db.Decimal(6, 2)
  exercise_price        Decimal   @db.Decimal(10, 6)
  scale                 Int       @db.SmallInt
  term                  Int       @db.SmallInt
  quote                 Decimal   @db.Decimal(6, 2)
  created_at            DateTime? @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  structure             String    @db.VarChar(8)
  expiry_date           DateTime? @db.Timestamptz(6)
  expiry_date_confirmed Boolean?  @default(false)
  quote_provider        String?   @db.VarChar(32)
  quote_diff            Decimal?  @db.Decimal(6, 2)
  orders                orders    @relation(fields: [trade_no], references: [trade_no], onDelete: NoAction, onUpdate: NoAction)
  users                 users     @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model stocks {
  ts_code String @id @db.VarChar(9)
  scale   Int
}

model system_auto_manage_config {
  manage_id  Int       @id @default(autoincrement())
  config     Json
  changed_at DateTime? @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
}

model system_status_history {
  status_id  Int       @id @default(autoincrement())
  status     Json
  admin_id   Int?
  changed_at DateTime? @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  admins     admins?   @relation(fields: [admin_id], references: [admin_id], onDelete: NoAction, onUpdate: NoAction)
}

model transactions {
  txn_id        Int              @id @default(autoincrement())
  user_id       Int
  type          transaction_type
  currency      currency_type    @default(CNY)
  created_at    DateTime?        @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  signed_amount Decimal          @db.Decimal(10, 2)
  trade_no      String?          @db.VarChar(20)
  orders        orders?          @relation(fields: [trade_no], references: [trade_no], onDelete: NoAction, onUpdate: NoAction)
  users         users            @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model users {
  user_id                          Int                   @id @default(autoincrement())
  phone_number                     String?               @unique @db.VarChar(32)
  password_hash                    String                @db.VarChar(60)
  is_qualified                     Boolean?              @default(false)
  email                            String                @unique @db.VarChar(128)
  balance_cny                      String                @db.VarChar(32)
  balance_hkd                      String                @db.VarChar(32)
  balance_usd                      String                @db.VarChar(32)
  created_at                       DateTime?             @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  updated_at                       DateTime?             @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  contribution                     Decimal?              @default(0.0) @db.Decimal(20, 2)
  name                             String?               @db.VarChar(128)
  id_number                        String?               @db.VarChar(64)
  bank_name                        String?               @db.VarChar(128)
  bank_account                     String?               @db.VarChar(64)
  bank_code                        String?               @db.VarChar(16)
  premium                          Decimal?              @default(0.0) @db.Decimal(20, 2)
  deposit                          Decimal?              @default(0.0) @db.Decimal(20, 2)
  can_transfer                     Boolean?              @default(false)
  payment_password_hash            String?               @db.VarChar(60)
  custom_quote_diffs               Json?
  custom_profit_sharing_percentage Decimal?              @db.Decimal(5, 2)
  audits                           audits[]
  inquiries                        inquiries[]
  manual_orders                    manual_orders[]
  notification_reads               notification_reads[]
  notifications                    notifications[]
  order_modifications              order_modifications[]
  orders                           orders[]
  pending_orders                   pending_orders[]
  positions                        positions[]
  transactions                     transactions[]
}

model order_modifications {
  id         Int                     @id @default(autoincrement())
  trade_no   String                  @db.VarChar(20)
  user_id    Int
  type       order_modification_type
  data       Json                    @default("{}")
  comment    String?
  created_at DateTime?               @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  admin_id   Int?
  admins     admins?                 @relation(fields: [admin_id], references: [admin_id], onDelete: NoAction, onUpdate: NoAction)
  orders     orders                  @relation(fields: [trade_no], references: [trade_no], onDelete: NoAction, onUpdate: NoAction)
  users      users                   @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model platform_configs {
  config_id  Int       @id @default(autoincrement())
  config     Json
  admin_id   Int?
  created_at DateTime? @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  admins     admins?   @relation(fields: [admin_id], references: [admin_id], onDelete: NoAction, onUpdate: NoAction)
}

model channel_transactions {
  transaction_id Int                        @id @default(autoincrement())
  channel_id     String
  amount         Decimal                    @db.Decimal(10, 2)
  type           channel_transaction_type
  status         channel_transaction_status @default(pending)
  currency       currency_type
  remarks        String?
  created_at     DateTime                   @default(now()) @db.Timestamptz(6)
  reviewed_at    DateTime?                  @db.Timestamptz(6)
  admin_id       Int?
  review_comment String?
  channels       channels                   @relation(fields: [channel_id], references: [channel_id], onDelete: NoAction, onUpdate: NoAction)
}

model channels {
  channel_id           String                 @id
  name                 String
  created_at           DateTime               @default(now()) @db.Timestamptz(6)
  is_locked            Boolean                @default(false)
  balance_cny          String                 @db.VarChar(32)
  balance_hkd          String                 @db.VarChar(32)
  balance_usd          String                 @db.VarChar(32)
  channel_transactions channel_transactions[]
}

model system_health_checks {
  check_id       Int       @id @default(autoincrement())
  timestamp      DateTime? @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  test_type      String    @db.VarChar(32)
  status         String    @db.VarChar(16)
  details        Json?     @default("{}")
  execution_time Int?
  error_message  String?
}

model bank_accounts {
  config_id  Int       @id @default(autoincrement())
  config     Json
  admin_id   Int?
  created_at DateTime? @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  admins     admins?   @relation(fields: [admin_id], references: [admin_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_admin")
}

model site_configs {
  config_id  Int      @id @default(autoincrement())
  config     Json
  admin_id   Int?
  created_at DateTime @default(now()) @db.Timestamptz(6)
  admins     admins?  @relation(fields: [admin_id], references: [admin_id], onUpdate: NoAction)
}

model assets {
  asset_id   String   @id @db.VarChar(50)
  content    Bytes
  mime_type  String   @db.VarChar(50)
  file_size  Int
  type       String   @db.VarChar(20)
  created_at DateTime @default(now()) @db.Timestamptz(6)
  admin_id   Int?
  admins     admins?  @relation(fields: [admin_id], references: [admin_id], onDelete: NoAction, onUpdate: NoAction)
}

model shared_configs {
  config_id  Int       @id @default(autoincrement())
  config     Json
  admin_id   Int?
  created_at DateTime? @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  admins     admins?   @relation(fields: [admin_id], references: [admin_id], onDelete: NoAction, onUpdate: NoAction)
}

model manual_orders {
  manual_order_id Int                 @id @default(autoincrement())
  user_id         Int
  ts_code         String              @db.VarChar(9)
  entry_price     Decimal             @db.Decimal(6, 2)
  exercise_price  Decimal             @db.Decimal(10, 6)
  scale           Int                 @db.SmallInt
  term            Int                 @db.SmallInt
  structure       String              @db.VarChar(8)
  quote           Decimal             @db.Decimal(6, 2)
  status          manual_order_status
  quote_provider  String?             @db.VarChar(32)
  created_at      DateTime            @default(dbgenerated("(now() AT TIME ZONE 'Asia/Shanghai'::text)")) @db.Timestamptz(6)
  expiry_date     DateTime?           @db.Timestamptz(6)
  settle_price    Decimal?            @db.Decimal(6, 2)
  remarks         String?
  entry_date      DateTime?           @db.Timestamptz(6)
  exit_date       DateTime?           @db.Timestamptz(6)
  users           users               @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

enum admin_permission {
  basic
  qualify
  finance
  config
  admin
  order_manage
}

enum audit_status {
  pending
  approved
  rejected
}

enum audit_type {
  qualification
  fund
}

enum currency_type {
  CNY
  HKD
  USD
}

enum inquiry_status {
  pending
  processing
  approved
  rejected
  expired
}

enum notification_target {
  system
  personal
}

enum notification_type {
  system
  feature
  account
  order
}

enum order_status {
  holding
  sold
  limit_buying
  limit_selling
  vwap_buying
  vwap_selling
}

enum transaction_type {
  buy
  sell
  deposit
  withdraw
  platform_deposit
  exchange
  transfer
}

enum order_modification_type {
  manual_create
  order_change
  manual_close
}

enum channel_transaction_status {
  pending
  confirmed
  rejected
  auto_confirmed
}

enum channel_transaction_type {
  deposit
  withdraw
  user_order
  user_execute
  exchange
}

enum manual_order_status {
  holding
  sold
}

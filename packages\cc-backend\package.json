{"name": "@ink/cc-backend", "version": "1.0.0", "description": "总控端后端服务", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsup", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:studio": "prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "socket.io": "^4.7.4", "node-ssh": "^13.1.0", "bull": "^4.12.2", "ioredis": "^5.3.2", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "multer": "^1.4.5-lts.1", "archiver": "^6.0.1", "tar": "^6.2.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@types/archiver": "^6.0.2", "@types/tar": "^6.1.9", "tsx": "^4.6.0", "tsup": "^8.0.1", "typescript": "^5.3.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0"}, "keywords": ["control-center", "deployment", "monitoring", "devops"], "author": "", "license": "MIT"}
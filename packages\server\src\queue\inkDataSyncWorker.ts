import type { Job } from "bullmq";
import { createWorker, inkDataSyncQueue, addRepeatedJob } from "./index.js";
import { getChildLogger } from "@/utils/logger.js";
import * as InkApi from "@/api/inkApi.js";
import { isMarketDay } from "@/financeUtils/marketTimeManager.js";
import { getChinaDateCompactString } from "@/utils/dateUtils.js";
import * as ConfigService from "@/services/admin/index.js";
import type { BusinessConfig } from "@packages/shared";

// 导入优化模块
import {
	INK_DATA_SYNC_JOBS,
	JOB_IDS,
	REDIS_KEYS,
	TradingTimeUtils,
} from "./ink/constants.js";
import { lockManager } from "./ink/distributedLockManager.js";
import { inkSyncConfig } from "./ink/config.js";
import { retryHandler } from "./ink/retryHandler.js";
import { redisManager } from "./ink/redisManager.js";
import { metricsCollector } from "./ink/metricsCollector.js";

const workerLogger = getChildLogger("InkDataSyncWorker");

/**
 * 操作装饰器函数 - 简化重复的模式
 */
function withLock<T extends unknown[]>(
	lockKey: string,
	operation: (...args: T) => Promise<void>,
) {
	return async (...args: T): Promise<void> => {
		const lockAcquired = await lockManager.acquireLockWithRenewal(lockKey);

		if (!lockAcquired) {
			workerLogger.info(`Operation skipped: lock already held for ${lockKey}`);
			return;
		}

		try {
			await operation(...args);
		} finally {
			await lockManager.releaseLock(lockKey);
		}
	};
}

function withMetrics<T extends unknown[]>(
	jobName: string,
	operation: (...args: T) => Promise<void>,
) {
	return async (...args: T): Promise<void> => {
		metricsCollector.recordJobStart(jobName);

		try {
			await operation(...args);
			metricsCollector.recordJobComplete(jobName, true);
		} catch (error) {
			metricsCollector.recordJobComplete(
				jobName,
				false,
				(error as Error).message,
			);
			throw error;
		}
	};
}

function withLockAndMetrics<T extends unknown[]>(
	lockKey: string,
	jobName: string,
	operation: (...args: T) => Promise<void>,
) {
	return withLock(lockKey, withMetrics(jobName, operation));
}

/**
 * 交易时间辅助函数
 */
function getCurrentTime(): number {
	const now = new Date();
	return now.getHours() * 100 + now.getMinutes();
}

function isInTradingHours(): boolean {
	const time = getCurrentTime();
	const config = inkSyncConfig;

	// 上午交易时段
	const morningTrading =
		time >= config.morningTradingStart && time < config.morningTradingEnd;
	// 下午交易时段
	const afternoonTrading =
		time >= config.afternoonTradingStart && time < config.afternoonTradingEnd;

	return morningTrading || afternoonTrading;
}

function isInPreMarketHours(): boolean {
	const time = getCurrentTime();
	const config = inkSyncConfig;
	return time >= config.premarketStartTime && time < config.premarketEndTime;
}

function isKeyTradingTime(): boolean {
	const time = getCurrentTime();
	const keyTimes = [
		inkSyncConfig.morningTradingStart,
		inkSyncConfig.morningTradingEnd,
		inkSyncConfig.afternoonTradingStart,
		inkSyncConfig.afternoonTradingEnd,
	];

	return keyTimes.some((keyTime) => Math.abs(time - keyTime) <= 1);
}

function isKeyPreMarketTime(): boolean {
	const time = getCurrentTime();
	const keyTimes = [
		inkSyncConfig.premarketStartTime,
		inkSyncConfig.premarketEndTime,
	];

	return keyTimes.some((keyTime) => Math.abs(time - keyTime) <= 1);
}

/**
 * 数据更新服务 - 简化后只关注核心数据更新逻辑
 */
class DataUpdateService {
	/**
	 * 更新INK波动数据
	 */
	async updateInkData(): Promise<void> {
		// 检查交易日
		if (!(await isMarketDay())) {
			workerLogger.info("Not a market day, skipping INK data update");
			await this.recordExecution("update-ink-data", {
				reason: "not-market-day",
			});
			return;
		}

		const date = getChinaDateCompactString();

		// 获取数据
		const swingData = await retryHandler.executeWithRetry(
			() => InkApi.fetchSwingData(date),
			{ maxAttempts: 3 },
			"ink-api-swing-data",
		);

		if (!swingData || swingData.size === 0) {
			workerLogger.warn(`No data available for ${date}`);
			await this.recordExecution("update-ink-data", {
				reason: "no-data",
				date,
			});
			return;
		}

		// 存储数据
		const swingRecord = this.mapToRecord(swingData);
		await redisManager.setSwingData(swingRecord);

		workerLogger.info(
			`Successfully updated swing data with ${swingData.size} entries`,
		);
		await this.recordExecution("update-ink-data", {
			entriesCount: swingData.size,
			date,
			success: true,
		});
	}

	/**
	 * 更新价格报价
	 */
	async updatePriceQuotes(forceUpdate = false): Promise<void> {
		if (!forceUpdate && !(await isMarketDay())) {
			workerLogger.info("Not a market day, skipping price quotes update");
			return;
		}

		const date = getChinaDateCompactString();
		const providers = Object.values(InkApi.PriceProvider);

		// 并发处理所有提供商
		const results = await this.processConcurrently(
			providers,
			(provider) => this.updateSingleProvider(provider, date, forceUpdate),
			inkSyncConfig.concurrencyLimit,
		);

		const successCount = results.filter((r) => r.success).length;
		workerLogger.info(
			`Price quotes update: ${successCount}/${providers.length} successful`,
		);
	}

	/**
	 * 更新业务配置
	 */
	async updateBusinessConfig(): Promise<void> {
		if (!(await isMarketDay())) {
			workerLogger.debug("Not a market day, skipping business config update");
			return;
		}

		const configKeys = [
			"OPTION_MULTIPLIER",
			"STOCK_SCALE_LIMIT",
			"TOTAL_SCALE_LIMIT",
			"CHANNEL_CREDIT_LIMIT",
			"DISCOUNT_MULTIPLIER",
		] as const;

		const currentConfig = await ConfigService.getConfig();
		const updates = await this.checkConfigUpdates(configKeys, currentConfig);

		if (Object.keys(updates).length > 0) {
			await ConfigService.updateConfig(updates);
			workerLogger.info("Business config updated", updates);
		} else {
			workerLogger.debug("No config changes detected");
		}
	}

	// 私有辅助方法
	private mapToRecord(swingData: Map<string, number>): Record<string, number> {
		const record: Record<string, number> = {};
		swingData.forEach((value, key) => {
			record[key] = value;
		});
		return record;
	}

	private async updateSingleProvider(
		provider: InkApi.PriceProviderValue,
		date: string,
		forceUpdate: boolean,
	): Promise<{ success: boolean; provider: string }> {
		try {
			// 检查缓存
			if (!forceUpdate && (await redisManager.getPriceQuotes(provider, date))) {
				metricsCollector.recordCacheHit();
				return { success: true, provider };
			}

			metricsCollector.recordCacheMiss();

			// 获取新数据
			const quoteData = await retryHandler.executeWithRetry(
				() => InkApi.fetchPriceQuotes(provider, date),
				{ maxAttempts: 3 },
				`price-quotes-${provider}`,
			);

			if (quoteData && quoteData.size > 0) {
				const quotesRecord = this.convertQuotesToRecord(quoteData);
				await redisManager.setPriceQuotes(provider, date, quotesRecord);
				workerLogger.info(
					`${provider}: Updated ${quoteData.size} quotes for ${date}`,
				);
				return { success: true, provider };
			}

			return { success: false, provider };
		} catch (error) {
			workerLogger.error(error, `Failed to update ${provider} quotes`);
			return { success: false, provider };
		}
	}

	private convertQuotesToRecord(
		quoteData: Map<string, InkApi.OptionPriceQuote>,
	): Record<string, Array<number | null>> {
		const quotesRecord: Record<string, Array<number | null>> = {};
		quoteData.forEach((quote, stockCode) => {
			quotesRecord[stockCode] = [
				quote.c100_2w,
				quote.c100_1m,
				quote.c100_2m,
				quote.c100_3m,
				quote.c103_2w,
				quote.c103_1m,
				quote.c103_2m,
				quote.c103_3m,
				quote.c105_2w,
				quote.c105_1m,
				quote.c105_2m,
				quote.c105_3m,
				quote.c110_2w,
				quote.c110_1m,
				quote.c110_2m,
				quote.c110_3m,
			];
		});
		return quotesRecord;
	}

	private async processConcurrently<T, R>(
		items: T[],
		processor: (item: T) => Promise<R>,
		concurrencyLimit: number,
	): Promise<R[]> {
		const results: R[] = [];

		for (let i = 0; i < items.length; i += concurrencyLimit) {
			const batch = items.slice(i, i + concurrencyLimit);
			const batchResults = await Promise.allSettled(batch.map(processor));

			for (const result of batchResults) {
				if (result.status === "fulfilled") {
					results.push(result.value);
				}
			}
		}

		return results;
	}

	private async checkConfigUpdates(
		configKeys: readonly (keyof BusinessConfig)[],
		currentConfig: BusinessConfig,
	): Promise<Partial<BusinessConfig>> {
		const updates: Partial<BusinessConfig> = {};

		const results = await Promise.allSettled(
			configKeys.map(async (key) => {
				const apiData = await retryHandler.executeWithRetry(
					() => InkApi.fetchBusinessConfig(key),
					{ maxAttempts: 2 },
					`business-config-${String(key)}`,
				);

				const newValue = apiData.get(key);
				const currentValue = currentConfig[key];

				if (this.isValidConfigValue(newValue) && currentValue !== newValue) {
					return { key, newValue, currentValue };
				}
				return null;
			}),
		);

		for (const result of results) {
			if (result.status === "fulfilled" && result.value) {
				const { key, newValue } = result.value;
				updates[key] = newValue;
			}
		}

		return updates;
	}

	private isValidConfigValue(value: unknown): value is number {
		return typeof value === "number" && !Number.isNaN(value) && value > 0;
	}

	private async recordExecution(
		taskName: string,
		details?: Record<string, unknown>,
	): Promise<void> {
		await redisManager.recordExecution(taskName, details);
	}
}

/**
 * 调度管理器 - 简化时间管理逻辑
 */
class ScheduleManager {
	/**
	 * 管理交易时段任务
	 */
	async manageMarketTimeUpdates(): Promise<void> {
		if (!isKeyTradingTime() && !isInTradingHours()) {
			return;
		}

		await this.updateScheduledJobs(
			INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE,
			isInTradingHours(),
			{
				jobId: JOB_IDS.HIGH_FREQ,
				interval: inkSyncConfig.highFreqUpdateInterval,
				description: "high-frequency",
			},
		);
	}

	/**
	 * 管理盘前报价任务
	 */
	async managePreMarketUpdates(): Promise<void> {
		if (!isKeyPreMarketTime() && !isInPreMarketHours()) {
			return;
		}

		await this.updateScheduledJobs(
			INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE,
			isInPreMarketHours(),
			{
				jobId: JOB_IDS.PREMARKET,
				interval: inkSyncConfig.premarketUpdateInterval,
				description: "premarket",
			},
		);
	}

	private async updateScheduledJobs(
		jobName: string,
		shouldRun: boolean,
		config: { jobId: string; interval: number; description: string },
	): Promise<void> {
		// 清理现有任务
		const tasksRemoved = await this.cleanupExistingJobs(jobName, config.jobId);

		if (shouldRun) {
			// 添加新任务
			await inkDataSyncQueue.add(
				jobName,
				{},
				{
					repeat: { every: config.interval, immediately: true },
					jobId: config.jobId,
					removeOnComplete: true,
				},
			);

			workerLogger.info(
				`Started ${config.description} updates (${config.interval}ms interval)`,
			);
			await redisManager.recordExecution(`manage-${config.description}`, {
				action: "started",
				tasksRemoved,
			});
		} else {
			// 在关闭时主动触发最后一次更新，确保数据完整性
			if (isKeyTradingTime() || isKeyPreMarketTime()) {
				workerLogger.info(
					`Triggering final ${config.description} update before stopping`,
				);

				try {
					// 根据任务类型执行最后一次更新
					if (jobName === INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE) {
						await dataService.updateInkData();
					} else if (
						jobName === INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE
					) {
						await dataService.updatePriceQuotes(false);
					}

					workerLogger.info(`Final ${config.description} update completed`);
				} catch (error) {
					workerLogger.error(
						error,
						`Failed to execute final ${config.description} update`,
					);
				}
			}

			workerLogger.info(`Stopped ${config.description} updates`);
			await redisManager.recordExecution(`manage-${config.description}`, {
				action: "stopped",
				tasksRemoved,
			});
		}
	}

	private async cleanupExistingJobs(
		jobName: string,
		jobId: string,
	): Promise<number> {
		const repeatableJobs = await inkDataSyncQueue.getJobSchedulers();
		let tasksRemoved = 0;

		for (const job of repeatableJobs) {
			if (job.name === jobName || job.id?.includes(jobId)) {
				await inkDataSyncQueue.removeJobScheduler(job.key);
				tasksRemoved++;
			}
		}

		return tasksRemoved;
	}
}

// 创建服务实例
const dataService = new DataUpdateService();
const scheduleManager = new ScheduleManager();

// 使用装饰器包装的操作函数
const updateInkDataWithLock = withLockAndMetrics(
	REDIS_KEYS.INK_DATA_UPDATE_LOCK,
	"updateInkData",
	() => dataService.updateInkData(),
);

const updatePriceQuotesWithLock = withLockAndMetrics(
	REDIS_KEYS.INK_QUOTES_UPDATE_LOCK,
	"updatePriceQuotes",
	(forceUpdate: boolean) => dataService.updatePriceQuotes(forceUpdate),
);

const updateBusinessConfigWithLock = withLockAndMetrics(
	REDIS_KEYS.INK_CONFIG_UPDATE_LOCK,
	"updateBusinessConfig",
	() => dataService.updateBusinessConfig(),
);

const manageMarketTimeWithLock = withLock(
	REDIS_KEYS.INK_MANAGE_MARKET_LOCK,
	() => scheduleManager.manageMarketTimeUpdates(),
);

const managePreMarketWithLock = withLock(
	REDIS_KEYS.INK_MANAGE_PREMARKET_LOCK,
	() => scheduleManager.managePreMarketUpdates(),
);

/**
 * 作业处理策略映射 - 替代复杂的switch语句
 */
const jobHandlers: Record<
	string,
	(data?: { forceUpdate?: boolean }) => Promise<void>
> = {
	[INK_DATA_SYNC_JOBS.UPDATE_INK_DATA]: updateInkDataWithLock,
	[INK_DATA_SYNC_JOBS.UPDATE_PRICE_QUOTES]: (data) =>
		updatePriceQuotesWithLock(data?.forceUpdate ?? false),
	[INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG]: updateBusinessConfigWithLock,
	[INK_DATA_SYNC_JOBS.HIGH_FREQ_INK_DATA_UPDATE]: async () => {
		if (await isMarketDay()) {
			await dataService.updateInkData();
		}
	},
	[INK_DATA_SYNC_JOBS.PREMARKET_PRICE_QUOTES_UPDATE]: () =>
		updatePriceQuotesWithLock(false),
	[INK_DATA_SYNC_JOBS.INITIALIZE_INK_DATA_SCHEDULES]:
		processInitializeInkDataSchedules,
};

/**
 * 主作业处理函数 - 简化后的版本
 */
async function processInkDataSyncJob(job: Job) {
	const { name, data } = job;

	workerLogger.info(`Processing job: ${name}`);

	// 处理管理类任务（有变体名称）
	if (name.startsWith(INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES)) {
		return manageMarketTimeWithLock();
	}

	if (name.startsWith(INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES)) {
		return managePreMarketWithLock();
	}

	// 处理标准任务
	const handler = jobHandlers[name];
	if (!handler) {
		throw new Error(`Unknown job type: ${name}`);
	}

	return handler(data);
}

/**
 * 初始化处理 - 简化版本
 */
async function processInitializeInkDataSchedules(): Promise<void> {
	// 只检查初始化任务的最近执行记录，而不是全局执行记录
	if (await redisManager.hasRecentExecution("initialize-schedules")) {
		workerLogger.info(
			"Skipping initialization due to recent initialization execution",
		);
		return;
	}

	await initializeInkDataSyncJobs();
	await redisManager.recordExecution("initialize-schedules", {
		message: "INK data sync jobs initialized successfully",
	});
	workerLogger.info("INK data sync jobs initialized successfully");
}

/**
 * 初始化所有调度任务
 */
export async function initializeInkDataSyncJobs() {
	workerLogger.info("Initializing INK data sync jobs");

	const cronExpressions = TradingTimeUtils.getCronExpressions();
	const jobOptions = { removeOnComplete: true, removeOnFail: 50 };
	const initialOptions = { removeOnComplete: true, removeOnFail: 5 };

	// 立即执行的初始任务
	const initialJobs = [
		{ name: INK_DATA_SYNC_JOBS.UPDATE_INK_DATA, data: {} },
		{
			name: INK_DATA_SYNC_JOBS.UPDATE_PRICE_QUOTES,
			data: { forceUpdate: true },
		},
		{ name: INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG, data: {} },
		{ name: INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES, data: {} },
		{ name: INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES, data: {} },
	];

	for (const job of initialJobs) {
		await inkDataSyncQueue.add(job.name, job.data, initialOptions);
	}

	// 定时任务
	const scheduledJobs = [
		// 管理任务
		{
			name: `${INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES}-start`,
			cron: cronExpressions.premarketStart,
		},
		{
			name: `${INK_DATA_SYNC_JOBS.MANAGE_PREMARKET_PRICE_QUOTES}-end`,
			cron: cronExpressions.premarketEnd,
		},
		{
			name: `${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-morning-start`,
			cron: cronExpressions.morningStart,
		},
		{
			name: `${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-morning-end`,
			cron: cronExpressions.morningEnd,
		},
		{
			name: `${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-afternoon-start`,
			cron: cronExpressions.afternoonStart,
		},
		{
			name: `${INK_DATA_SYNC_JOBS.MANAGE_MARKET_TIME_UPDATES}-afternoon-end`,
			cron: cronExpressions.afternoonEnd,
		},

		// 定期更新任务
		{ name: INK_DATA_SYNC_JOBS.UPDATE_INK_DATA, cron: "*/30 7-8,15-23 * * *" },
		{ name: INK_DATA_SYNC_JOBS.UPDATE_BUSINESS_CONFIG, cron: "0 * * * *" },
	];

	for (const job of scheduledJobs) {
		await addRepeatedJob(inkDataSyncQueue, job.name, {}, job.cron, jobOptions);
	}

	workerLogger.info("INK data sync jobs scheduled successfully");
}

/**
 * 应用启动时初始化
 *
 * ⚠️ **BullMQ 作业持久化注意事项**：
 *
 * BullMQ 会将作业持久化到 Redis 中，包括失败的作业。当应用重启时，BullMQ 会尝试恢复之前的作业。
 * 这可能导致以下问题：
 *
 * 1. **僵尸作业**：之前因连接问题失败的作业可能卡在队列中，重启时尝试恢复但继续失败
 * 2. **作业重复**：如果旧作业没有正确清理，可能与新作业冲突
 * 3. **状态不一致**：作业的内部状态可能与实际执行状态不符
 *
 * **调整策略**：
 * - 设置合理的 removeOnComplete 和 removeOnFail 参数
 * - 添加作业恢复检查逻辑
 *
 * **解决方式**：
 * - 检查 Redis 中 `bull:{queueName}:*` 键的内容
 * - 查看 BullMQ 管理界面中的作业状态
 * - 手动删除有问题的作业键：`DEL bull:app-{APP_ID}-bullmq-ink-data-sync:{jobId}`
 */
export async function initializeOnStartup() {
	try {
		workerLogger.info("Starting INK data worker initialization...");

		// 检查队列连接状态
		try {
			await inkDataSyncQueue.waitUntilReady();
			workerLogger.info("INK data sync queue connection verified");
		} catch (error) {
			workerLogger.error(error, "Failed to connect to INK data sync queue");
			// 即使连接失败，我们也继续尝试，因为重试机制可能会解决问题
		}

		// 验证锁配置
		const lockValidation = await validateLockConfiguration();
		workerLogger.info("Lock configuration validation:", lockValidation);

		if (!lockValidation.isValid) {
			workerLogger.error("Lock configuration validation failed");
		}

		// 添加初始化作业 - 使用更多重试选项
		workerLogger.info("Adding INITIALIZE_INK_DATA_SCHEDULES job to queue...");
		await inkDataSyncQueue.add(
			INK_DATA_SYNC_JOBS.INITIALIZE_INK_DATA_SCHEDULES,
			{},
			{
				jobId: "init-ink-data-schedules",
				removeOnComplete: true,
				removeOnFail: 5,
				// 添加重试配置，防止因临时连接问题导致作业失败
				attempts: 3,
				backoff: {
					type: "exponential",
					delay: 2000,
				},
			},
		);

		workerLogger.info(
			"INK data schedule initialization job added to queue successfully",
		);
	} catch (error) {
		workerLogger.error(
			error,
			"Failed to initialize INK data worker on startup",
		);

		// 不要抛出错误，因为这可能导致整个应用启动失败
		// 相反，记录错误并允许应用继续启动
		// 系统可能在稍后通过其他机制恢复
		workerLogger.warn(
			"INK data worker initialization failed, but application will continue starting",
		);
	}
}

/**
 * 验证分布式锁配置
 */
export async function validateLockConfiguration() {
	const warnings: string[] = [];
	const recommendations: string[] = [];

	const lockDiagnosis = await lockManager.diagnoseLock("test-validation-lock");
	const appIdentifier = lockDiagnosis.currentAppId;

	// 检查应用标识符
	if (appIdentifier.startsWith("unknown:")) {
		warnings.push("Using random app identifier");
		recommendations.push("Configure tradingPlatformId or channelId");
	}

	// 测试锁功能
	let isValid = true;
	try {
		const testLock = `test-validation-lock-${Date.now()}`;
		const acquired = await lockManager.acquireLockWithRenewal(testLock, 5, 0);
		if (acquired) {
			await lockManager.releaseLock(testLock);
		} else {
			isValid = false;
			warnings.push("Lock acquisition test failed");
		}
	} catch (error) {
		isValid = false;
		warnings.push(`Lock test error: ${(error as Error).message}`);
	}

	return { isValid, appIdentifier, warnings, recommendations };
}

// 创建Worker实例
export const inkDataSyncWorker = createWorker(
	inkDataSyncQueue,
	processInkDataSyncJob,
);

// 兼容性导出
export async function getCachedSwingData(): Promise<Record<
	string,
	number
> | null> {
	return redisManager.getSwingData();
}

export async function getCachedOptionPrice(
	stockCode: string,
	provider: InkApi.PriceProviderValue,
	strikePercent: 100 | 103 | 105 | 110,
	period: "2w" | "1m" | "2m" | "3m",
): Promise<number | null> {
	const date = getChinaDateCompactString();
	return redisManager.getOptionPrice(
		stockCode,
		provider,
		date,
		strikePercent,
		period,
	);
}

// 导出新功能
export {
	metricsCollector,
	redisManager,
	inkSyncConfig,
	retryHandler,
	lockManager,
};

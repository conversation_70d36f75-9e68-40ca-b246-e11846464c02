import { AppError } from "@/core/appError.js";
import * as User from "@/models/user.js";
import * as Order from "@/models/trade/order.js";
import * as FundService from "../fundService.js";
import * as PositionService from "../positionService.js";
import {
	fetchCurrentPrice,
	fetchPriceDetails,
} from "@/financeUtils/marketData.js";
import {
	OrderStatus,
	TransactionType,
	TradeDirection,
	Currency,
	WebSocketMessageType,
	ChannelTransactionType,
	ChannelTransactionStatus,
} from "@packages/shared";
import type {
	OrderData,
	OrderRequest,
	BuyRequest,
	SellRequest,
	OrderType,
} from "@packages/shared";
import type { Prisma } from "@prisma/client";
import {
	withTransaction,
	withCrossDbTransaction2,
} from "@/core/dbTxnManager.js";
import { calcBuyAmount, calcSellProfit } from "@/financeUtils/calculator.js";
import logger from "@/utils/logger.js";
import * as OrderModel from "@/models/trade/order.js";
import { calculateEstimatedExpiryDate } from "@/financeUtils/marketTimeManager.js";
import EmailService from "@/utils/email.js";
import { notifyUser } from "@/utils/notify.js";
import * as ChannelFundModel from "@/models/channelFund.js";
import { isChannel } from "@/config/configManager.js";
import { PROVIDER_DISCOUNT_CONFIG } from "@/config/defaultParams.js";
import * as configService from "@/services/admin/configService.js";
import { updateChannelTransactionRemarks } from "@/models/channelFund.js";

/**
 * @param orderType 用于限价和均价执行 websocket 通知
 */
export async function executeOrder(
	orderRequest: OrderRequest,
	options?: {
		isExpiry?: boolean;
		vwapPrice?: number;
		orderType?: OrderType;
	},
): Promise<OrderData> {
	logger.info(orderRequest, "执行订单");
	if (orderRequest.direction === TradeDirection.BUY) {
		return buyOrder(orderRequest as BuyRequest, options);
	}

	const order = await OrderModel.findByTradeNo(orderRequest.trade_no);
	const { price } = await fetchCurrentPrice(order.ts_code);

	// 根据期权类型判断价格条件，等于不让卖
	const isCall = order.structure.endsWith("C");
	const isPriceValid = isCall
		? price > order.exercise_price // 看涨期权：市价需要高于执行价
		: price < order.exercise_price; // 看跌期权：市价需要低于执行价

	// 如果是到期卖出，则不验证价格
	if (!isPriceValid && !options?.isExpiry) {
		const errorMessage = isCall
			? "Current price is below exercise price"
			: "Current price is above exercise price";
		throw AppError.create("INVALID_EXERCISE_PRICE", errorMessage);
	}

	return sellOrder(orderRequest as SellRequest, options);
}

/**
 * 检查价格波动是否超过限制
 * 对于非INK提供商，如果股票价格波动超过5%，则报价失效
 * @param ts_code 股票代码
 * @param provider 报价提供商
 * @returns Promise<void>
 * @throws AppError 如果价格波动超过限制
 */
async function checkPriceVolatility(
	ts_code: string,
	provider: string,
): Promise<void> {
	// 只对非INK提供商检查价格波动
	if (provider === "INK") {
		return;
	}

	// 获取价格详情
	const priceDetails = await fetchPriceDetails(ts_code);
	const { high, low, pre_close } = priceDetails;

	// 计算价格波动百分比
	const highVolatility = Math.abs(high - pre_close) / pre_close;
	const lowVolatility = Math.abs(low - pre_close) / pre_close;
	const volatilityThreshold = 0.05; // 5%

	logger.debug(
		`Price volatility check for ${ts_code}: high=${high}, low=${low}, pre_close=${pre_close}`,
	);
	// 如果价格波动超过5%，抛出错误
	if (
		highVolatility > volatilityThreshold ||
		lowVolatility > volatilityThreshold
	) {
		throw AppError.create(
			"PRICE_VOLATILITY_EXCEEDED",
			"股票价格波动超过限制，当前报价已失效",
			{
				provider,
				ts_code,
				high,
				low,
				pre_close,
			},
		);
	}
}

// 提取核心业务逻辑到单独函数
async function buyOrderCore(
	buyRequest: BuyRequest,
	tx: Prisma.TransactionClient,
	options?: {
		vwapPrice?: number;
		orderType?: OrderType;
	},
): Promise<OrderData> {
	const { user_id, scale, quote } = buyRequest;
	const amount = calcBuyAmount(scale, quote);

	// 检查价格波动 - 对于非INK提供商
	await checkPriceVolatility(
		buyRequest.ts_code,
		buyRequest.quote_provider || "INK",
	);

	// 获取入场价和执行价
	const entry_price = options?.vwapPrice
		? options.vwapPrice
		: (await fetchCurrentPrice(buyRequest.ts_code)).price;
	const exercise_price =
		(entry_price * Number.parseInt(buyRequest.structure)) / 100;

	// 计算到期日
	const { expiryDate, isConfirmed } = await calculateEstimatedExpiryDate(
		new Date(),
		buyRequest.term,
	);

	// Create holding status order
	const order = await Order.create(
		{
			...buyRequest,
			total_scale: buyRequest.scale,
			entry_price,
			structure: buyRequest.structure,
			exercise_price,
			status: OrderStatus.HOLDING,
			settle_price: 0, // 0 for holding status
			expiry_date: expiryDate.toISOString(),
			expiry_date_confirmed: isConfirmed,
			quote_provider: buyRequest.quote_provider || "INK", // Use provided quote_provider or default to 'INK'
			quote_diff: buyRequest.quote_diff, // Include quote difference
		},
		tx,
	);

	// Deduct user balance
	await FundService.handleTransaction(
		{
			user_id,
			amount,
			type: TransactionType.BUY,
			currency: Currency.CNY,
			trade_no: order.trade_no,
		},
		tx,
	);

	// Add contribution points when order is created, 1 point per 10000 CNY
	await User.addContributionPoints(user_id, scale, tx);
	// Add accumulated premium when order is created, unit is CNY
	await User.addAccumulatedPremium(user_id, amount, tx);

	// Create position record
	await PositionService.createOrUpdatePosition(
		user_id,
		order.ts_code,
		scale,
		entry_price,
		buyRequest.structure,
		exercise_price,
		order.trade_no,
		tx,
		buyRequest.quote_provider || "INK", // Pass quote_provider to position creation
		buyRequest.quote_diff, // Include quote difference
	);

	const user = await User.findById(user_id);
	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}
	EmailService.sendEmail(user.email, "ORDER_INSTRUCTION", {});

	await notifyUser(user_id, {
		type: WebSocketMessageType.ORDER_UPDATE,
		data: options?.orderType
			? { orderType: options.orderType, direction: TradeDirection.BUY }
			: undefined,
	});

	return order;
}

// 修改入口函数
export async function buyOrder(
	buyRequest: BuyRequest,
	options?: {
		vwapPrice?: number;
		orderType?: OrderType;
	},
): Promise<OrderData> {
	// 通道版本走专用入口
	if (isChannel()) {
		return buyOrderWithChannel(buyRequest, options);
	}

	return withTransaction(async (tx) => {
		return buyOrderCore(buyRequest, tx, options);
	});
}

/**
 * 使用跨数据库事务处理通道版本的买入订单
 */
async function buyOrderWithChannel(
	buyRequest: BuyRequest,
	options?: {
		vwapPrice?: number;
		orderType?: OrderType;
	},
): Promise<OrderData> {
	const { user_id, scale, quote } = buyRequest;

	// 预先计算订单所需金额
	const requiredAmount = calcBuyAmount(scale, quote);

	// 在执行事务前检查用户余额是否充足
	const userBalance = await User.getBalance(user_id, Currency.CNY);
	if (userBalance < requiredAmount) {
		throw AppError.create(
			"INSUFFICIENT_BALANCE",
			`用户余额不足，需要 ${requiredAmount.toFixed(2)}，当前可用 ${userBalance.toFixed(2)}`,
			{
				required: requiredAmount,
				available: userBalance,
			},
		);
	}

	// 存储平台事务创建的占位记录ID
	let channelOrderTransactionId: number | null = null;

	// 平台数据库事务函数 - 创建占位记录
	const platformDbFn = async (platformTx: Prisma.TransactionClient) => {
		// 预计算需要的值
		const { scale, quote, quote_diff, quote_provider } = buyRequest;

		// 计算基础金额（用户实际支付金额）
		const userPayAmount = quote * scale * 100;

		// 计算通道实际支付金额（用户支付金额减去提价和折扣）
		let channelPayAmount = userPayAmount;

		// 提价佣金金额
		let commissionAmount = 0;
		// 折扣金额
		let discountAmount = 0;

		// 减去提价部分（如果有）
		if (quote_diff && quote_diff > 0) {
			commissionAmount = quote_diff * scale * 100;
			channelPayAmount -= commissionAmount;
		}

		// 减去提供商折扣部分（如果有）
		const provider = quote_provider || "INK";
		let discountRate = 0;
		if (provider in PROVIDER_DISCOUNT_CONFIG) {
			discountRate = 1 - PROVIDER_DISCOUNT_CONFIG[provider];
			const baseAmount = (quote - (quote_diff || 0)) * scale * 100;
			discountAmount = baseAmount * discountRate;
			channelPayAmount -= discountAmount;
		}

		// 创建详细的交易备注，包含提价和折扣信息
		let detailedRemarks = "用户下单 - 待更新订单号";
		if (commissionAmount > 0 || discountAmount > 0) {
			detailedRemarks += ` | 提价: ${commissionAmount > 0 ? commissionAmount.toFixed(2) : "0"}`;
			detailedRemarks += ` | 折扣: ${discountAmount > 0 ? discountAmount.toFixed(2) : "0"}${discountRate ? `(${(discountRate * 100).toFixed(0)}%)` : ""}`;
		}

		// 创建通道支出记录 - 直接设置为AUTO_CONFIRMED状态，立即处理余额变更
		const channelTransaction = await ChannelFundModel.createTransaction(
			{
				type: ChannelTransactionType.USER_ORDER,
				currency: Currency.CNY,
				amount: -Math.abs(channelPayAmount), // 确保为负数，表示通道支出
				status: ChannelTransactionStatus.AUTO_CONFIRMED, // 直接创建为已确认状态
				remarks: detailedRemarks,
			},
			platformTx,
		);
		channelOrderTransactionId = channelTransaction.transaction_id;

		// 返回所需参数和占位记录ID
		return {
			buyRequest,
			options,
			placeholderIds: {
				channelOrderId: channelOrderTransactionId,
			},
		};
	};

	// 主数据库事务函数 - 处理核心订单逻辑
	const mainDbFn = async (
		params: {
			buyRequest: BuyRequest;
			options?: { vwapPrice?: number; orderType?: OrderType };
			placeholderIds: {
				channelOrderId: number | null;
			};
		},
		tx: Prisma.TransactionClient,
	) => {
		// 使用核心逻辑创建订单
		const order = await buyOrderCore(params.buyRequest, tx, params.options);
		return order;
	};

	// 平台事务回滚函数 - 当主数据库事务失败时回滚平台数据库的变更
	const platformRollbackFn = async (
		params: {
			placeholderIds: {
				channelOrderId: number | null;
			};
		},
		error: unknown,
	) => {
		// 如果存在交易ID，创建一个补偿交易记录来抵消之前的交易
		if (params.placeholderIds.channelOrderId) {
			const txId = params.placeholderIds.channelOrderId;

			// 获取原始交易记录以确定金额
			const originalTransaction = await ChannelFundModel.getTransactionById(
				String(txId),
				true,
			);

			if (originalTransaction) {
				// 创建一个完全抵消原交易的补偿交易
				// 原交易是负数（支出），补偿交易是正数（收入）
				await ChannelFundModel.createTransaction({
					type: ChannelTransactionType.USER_ORDER, // 使用USER_ORDER类型，但表示为补偿
					currency: Currency.CNY,
					// 取符号相反的金额：原交易为正（收入），补偿为负（支出）
					amount: -originalTransaction.amount, // 如果原始是 +100，这里就是 -100
					status: ChannelTransactionStatus.AUTO_CONFIRMED,
					remarks: `自动补偿 - 主数据库事务失败 - 原交易ID: ${String(txId)}`,
				});

				// 更新原始交易备注，标记其已被补偿
				await ChannelFundModel.updateChannelTransactionRemarks(
					txId,
					`${originalTransaction.remarks} | 已补偿(主数据库事务失败)`,
					true,
				);

				logger.info(
					{
						originalTxId: txId,
						amount: originalTransaction.amount,
						error: error instanceof Error ? error.message : String(error),
					},
					"已创建补偿交易记录，成功回滚平台数据库变更",
				);
			} else {
				throw new Error(`无法找到需要补偿的原始交易记录: ${txId}`);
			}
		}
	};

	try {
		// 执行跨数据库事务，添加回滚函数作为第三个参数
		const order = await withCrossDbTransaction2(
			platformDbFn,
			mainDbFn,
			platformRollbackFn,
		);
		const { user_id, trade_no } = order;

		// 主事务成功后，更新平台数据库中的交易记录的备注
		// 注意：这里只更新备注，不会影响余额
		try {
			// 更新通道订单记录的备注信息
			if (channelOrderTransactionId) {
				// 计算更详细的备注，保留价格和折扣信息
				const { scale, quote, quote_diff, quote_provider } = buyRequest;

				// 基础金额计算
				const userPayAmount = quote * scale * 100;
				let channelPayAmount = userPayAmount;
				let commissionAmount = 0;
				let discountAmount = 0;

				// 计算提价佣金（如果有）
				if (quote_diff && quote_diff > 0) {
					commissionAmount = quote_diff * scale * 100;
					channelPayAmount -= commissionAmount;
				}

				// 计算提供商折扣（如果有）
				const provider = quote_provider || "INK";
				let discountRate = 0;
				if (provider in PROVIDER_DISCOUNT_CONFIG) {
					discountRate = 1 - PROVIDER_DISCOUNT_CONFIG[provider];
					const baseAmount = (quote - (quote_diff || 0)) * scale * 100;
					discountAmount = baseAmount * discountRate;
					channelPayAmount -= discountAmount;
				}

				// 构建详细备注
				let detailedRemarks = `用户下单 - 订单号: ${trade_no}`;
				if (commissionAmount > 0) {
					detailedRemarks += ` | 提价: ${commissionAmount.toFixed(2)}`;
				}
				if (discountAmount > 0) {
					detailedRemarks += ` | 折扣: ${discountAmount.toFixed(2)}(${(discountRate * 100).toFixed(0)}%)`;
				}

				// 只更新备注，不更新状态（状态已在创建时设为AUTO_CONFIRMED）
				await updateChannelTransactionRemarks(
					channelOrderTransactionId,
					detailedRemarks,
					true,
				);
			}
		} catch (updateError) {
			// 记录错误但不影响主流程
			logger.error(
				{
					trade_no,
					user_id,
					error:
						updateError instanceof Error
							? updateError.message
							: String(updateError),
				},
				"更新平台交易备注失败 - 需要手动处理",
			);
		}

		return order;
	} catch (error) {
		logger.error(
			{
				error: error instanceof Error ? error.message : String(error),
				buyRequest,
			},
			"订单创建失败 - 可能需要手动处理",
		);
		throw error;
	}
}

export async function sellOrder(
	sellRequest: SellRequest,
	options?: {
		vwapPrice?: number;
		orderType?: OrderType;
	},
): Promise<OrderData> {
	const { ts_code } = sellRequest;

	const UsedSellRequest = sellRequest as SellRequest & { settle_price: number };

	// 均价覆盖结算价格
	UsedSellRequest.settle_price = options?.vwapPrice
		? options.vwapPrice
		: (await fetchCurrentPrice(ts_code)).price;

	if (isChannel()) {
		return sellOrderWithChannel(UsedSellRequest, options?.orderType);
	}

	return withTransaction(async (tx) => {
		return sellOrderCore(UsedSellRequest, tx, options?.orderType);
	});
}

export async function sellOrderCore(
	sellRequest: SellRequest & { settle_price: number },
	client: Prisma.TransactionClient,
	orderType?: OrderType,
	userProfit?: number,
): Promise<OrderData> {
	const { trade_no, user_id, ts_code, scale, settle_price } = sellRequest;

	// 在事务中进行验证，并使用悲观锁
	const order = await validateSellOrder(
		trade_no,
		user_id,
		ts_code,
		scale,
		client,
	);

	// 直接处理订单售出逻辑（合并了processOrderSell函数）
	// 全单售出
	if (scale === order.scale) {
		const updatedOrder = await Order.update(
			order.trade_no,
			{ status: OrderStatus.SOLD, settle_price },
			client,
		);

		await processSellTransaction(
			order,
			settle_price,
			scale,
			client,
			orderType,
			userProfit,
		);

		return updatedOrder;
	}

	// 部分售出
	// prisma自动进行iso的时区转换
	const newSellOrder = await Order.create(
		{
			// Spread operator includes only type-compatible properties
			...order,
			scale: scale,
			status: OrderStatus.SOLD,
			settle_price,
			created_at: order.created_at,
			closed_at: new Date().toISOString(),
			is_split: true,
		},
		client,
	);

	const remainingScale = order.scale - scale;
	await Order.update(order.trade_no, { scale: remainingScale }, client);

	await processSellTransaction(
		newSellOrder,
		settle_price,
		scale,
		client,
		orderType,
	);

	return newSellOrder;
}

async function validateSellOrder(
	trade_no: string,
	user_id: number,
	ts_code: string,
	scale: number,
	client: Prisma.TransactionClient,
) {
	// 使用 SELECT FOR UPDATE 锁定记录，持续到整个事务结束
	const order = await client.$queryRaw<OrderModel.PrismaOrderData[]>`
		SELECT * FROM orders WHERE trade_no = ${trade_no} FOR UPDATE
	`.then((orders) =>
		orders.length > 0 ? OrderModel.transformOrderData(orders[0]) : null,
	);

	if (!order) {
		throw AppError.create("NOT_FOUND", "Order not found");
	}

	// 验证订单状态和所有权
	if (
		order.status !== OrderStatus.HOLDING ||
		order.user_id !== user_id ||
		order.ts_code !== ts_code
	) {
		throw AppError.create("INVALID_ORDER_STATUS", "Invalid order status", {
			order,
		});
	}

	// 验证卖出数量不超过订单数量
	if (scale > order.scale) {
		throw AppError.create(
			"INSUFFICIENT_POSITION",
			`Insufficient position for the order ${trade_no}`,
			{ required: scale, available: order.scale },
		);
	}

	return order;
}

/**
 * 卖出订单核心处理逻辑
 * @param orderType 非末位可选参数，不要省略
 */
async function processSellTransaction(
	order: OrderData,
	settle_price: number,
	scale: number,
	client: Prisma.TransactionClient,
	orderType?: OrderType,
	overrideProfit?: number, // 新参数：如果提供，则使用这个值而不是重新计算
) {
	// 如果提供了覆盖盈利值，直接使用，否则计算
	const sellProfit =
		overrideProfit !== undefined
			? overrideProfit
			: calcSellProfit(
					scale,
					settle_price,
					order.entry_price,
					order.exercise_price,
					order.structure,
				);

	await FundService.handleTransaction(
		{
			user_id: order.user_id,
			amount: sellProfit,
			type: TransactionType.SELL,
			currency: Currency.CNY,
			trade_no: order.trade_no,
		},
		client,
	);

	// 直接使用原始订单号更新持仓
	await PositionService.createOrUpdatePosition(
		order.user_id,
		order.ts_code,
		-scale,
		order.entry_price,
		order.structure,
		order.exercise_price,
		order.is_split ? order.trade_no.split("_")[0] : order.trade_no, // 如果是拆分订单就用原始单号
		client,
	);

	const user = await User.findById(order.user_id);
	if (!user) {
		throw AppError.create("USER_NOT_FOUND", "User not found");
	}

	EmailService.sendEmail(user.email, "EXERCISE_INSTRUCTION", {});

	await notifyUser(order.user_id, {
		type: WebSocketMessageType.ORDER_UPDATE,
		data: orderType
			? { orderType: orderType, direction: TradeDirection.SELL }
			: undefined,
	});

	return order;
}

/**
 * 处理通道版本的卖出订单结算，包括盈利分成
 */
async function sellOrderWithChannel(
	sellRequest: SellRequest & { settle_price: number },
	orderType?: OrderType,
) {
	const { scale, settle_price, trade_no, user_id } = sellRequest;

	const order = await Order.findByTradeNo(trade_no);
	// 计算卖出总利润
	const totalProfit = calcSellProfit(
		scale,
		settle_price,
		order.entry_price,
		order.exercise_price,
		order.structure,
	);

	// 只处理有盈利的订单
	if (totalProfit <= 0) {
		// 无盈利或亏损，直接使用核心逻辑处理全部结算
		return withTransaction(async (tx) => {
			return sellOrderCore(sellRequest, tx, orderType);
		});
	}

	// 获取用户信息以检查自定义盈利分成
	const user = await User.findById(user_id);
	if (!user) {
		throw AppError.create(
			"USER_NOT_FOUND",
			`User ${user_id} not found for profit sharing calculation.`,
		);
	}

	// 获取平台配置，检查是否启用盈利分成，否则跳过所有分成计算逻辑
	const platformConfig = await configService.getPlatformConfig();
	let userProfit = totalProfit; // 初始化用户盈利为总盈利，后续根据分成情况调整
	let platformSharingAmount = 0; // 初始化平台分成金额为0
	let actualSharingPercentage = 0; // 记录实际采纳的分成百分比，用于备注
	const premiumForSoldPortion = calcBuyAmount(scale, order.quote); // 计算本次卖出部分对应的原始期权费成本

	let effectiveSharingPercentage: number | undefined | null = undefined; // 存储最终决策使用的分成比例

	// 仅当平台启用了盈利分成功能时，才进一步判断具体的分成策略
	if (platformConfig?.profit_sharing?.enabled) {
		// 平台已启用盈利分成，现在确定使用用户自定义比例还是平台默认比例
		if (
			user.custom_profit_sharing_percentage !== undefined &&
			user.custom_profit_sharing_percentage !== null
		) {
			// 用户有自定义设置，优先使用用户设置
			effectiveSharingPercentage = user.custom_profit_sharing_percentage;
		} else if (platformConfig.profit_sharing.percentage !== undefined) {
			// 用户无自定义设置，但平台定义了默认分成比例，则使用平台默认比例
			effectiveSharingPercentage = platformConfig.profit_sharing.percentage;
		}
		// 若平台启用分成，但用户无自定义且平台也未定义具体比例，则effectiveSharingPercentage保持undefined，不进行分成

		// 仅当确定了有效的分成比例，并且总盈利大于期权费时，才实际计算分成金额
		if (
			effectiveSharingPercentage !== undefined &&
			effectiveSharingPercentage !== null &&
			totalProfit > premiumForSoldPortion
		) {
			actualSharingPercentage = effectiveSharingPercentage; // 记录用于计算的百分比
			// 分成金额 = (总盈利 - 期权费) * 分成比例
			const profitAbovePremium = totalProfit - premiumForSoldPortion; // 计算超出期权费部分的盈利
			platformSharingAmount =
				profitAbovePremium * (actualSharingPercentage / 100); // 计算平台应分得的金额
			userProfit = totalProfit - platformSharingAmount; // 用户最终获得的盈利 = 总盈利 - 平台分成
		}
		// 如果 totalProfit <= premiumForSoldPortion, platformSharingAmount 保持为 0, userProfit 保持为 totalProfit
	}

	// 通道方实际收到的金额仍为总盈利（这是通道账目上的记录，平台内部如何分成对通道初始入账无影响）
	const channelReceiveAmount = totalProfit;

	// 构建通道交易记录的初始备注，后续会根据订单号和分成情况补充完整
	const initialRemarks = "用户结算 - 待更新订单信息";

	let channelExecuteTransactionId: number | null = null;

	const platformDbFn = async (platformTx: Prisma.TransactionClient) => {
		const executeTransaction = await ChannelFundModel.createTransaction(
			{
				type: ChannelTransactionType.USER_EXECUTE,
				currency: Currency.CNY,
				amount: Math.abs(channelReceiveAmount), // 确保为正数，表示通道收入
				status: ChannelTransactionStatus.AUTO_CONFIRMED, // 直接创建为已确认状态
				remarks: initialRemarks,
			},
			platformTx, // 使用同一事务，确保原子性
		);

		channelExecuteTransactionId = executeTransaction.transaction_id;

		return {
			sellRequest,
			orderType,
			placeholderIds: {
				channelOrderId: channelExecuteTransactionId,
			},
			userProfit,
		};
	};

	// 主数据库事务函数 - 处理核心订单逻辑
	const mainDbFn = async (
		params: {
			sellRequest: SellRequest & { settle_price: number };
			orderType?: OrderType;
			placeholderIds: { channelOrderId: number | null };
			userProfit: number;
		},
		tx: Prisma.TransactionClient,
	) => {
		// 使用核心逻辑处理基本的卖出流程，但盈利已扣除分成
		return sellOrderCore(
			params.sellRequest,
			tx,
			params.orderType,
			params.userProfit, // 传入用户实际盈利
		);
	};

	// 平台事务回滚函数 - 当主数据库事务失败时回滚平台数据库的变更
	const platformRollbackFn = async (
		params: {
			placeholderIds: {
				channelOrderId: number | null;
			};
		},
		error: unknown,
	) => {
		// 如果存在交易ID，创建一个补偿交易记录来抵消之前的交易
		if (params.placeholderIds.channelOrderId) {
			const txId = params.placeholderIds.channelOrderId;

			// 获取原始交易记录以确定金额
			const originalTransaction = await ChannelFundModel.getTransactionById(
				String(txId),
				true,
			);

			if (originalTransaction) {
				// 创建一个完全抵消原交易的补偿交易
				// 原交易是正数（收入），补偿交易是负数（支出）
				await ChannelFundModel.createTransaction({
					type: ChannelTransactionType.USER_EXECUTE, // 使用USER_EXECUTE类型，但表示为补偿
					currency: Currency.CNY,
					// 取符号相反的金额：原交易为正（收入），补偿为负（支出）
					amount: -originalTransaction.amount, // 如果原始是 +100，这里就是 -100
					status: ChannelTransactionStatus.AUTO_CONFIRMED,
					remarks: `自动补偿 - 主数据库事务失败 - 原交易ID: ${String(txId)}`,
				});

				// 更新原始交易备注，标记其已被补偿
				await ChannelFundModel.updateChannelTransactionRemarks(
					txId,
					`${originalTransaction.remarks} | 已补偿(主数据库事务失败)`,
					true,
				);

				logger.info(
					{
						originalTxId: txId,
						amount: originalTransaction.amount,
						error: error instanceof Error ? error.message : String(error),
					},
					"已创建补偿交易记录，成功回滚平台数据库变更",
				);
			} else {
				throw new Error(`无法找到需要补偿的原始交易记录: ${txId}`);
			}
		}
	};

	try {
		// 执行跨数据库事务，添加回滚函数作为第三个参数
		const executedOrderResult = await withCrossDbTransaction2(
			platformDbFn,
			mainDbFn,
			platformRollbackFn,
		);

		// 如果已创建通道执行记录，事务完成后异步更新详细备注
		try {
			if (channelExecuteTransactionId) {
				// 生成详细的分成信息备注，包含更多交易细节
				let remarksDetail = `用户结算 - 订单号: ${executedOrderResult.trade_no}`;
				// 盈利且产生分成时更新备注
				if (platformSharingAmount > 0) {
					// platformSharingAmount 正确反映了是否产生了分成
					remarksDetail += ` | 期权费: ${premiumForSoldPortion.toFixed(2)}`;
					remarksDetail += ` | 平台分成: ${platformSharingAmount.toFixed(2)}(${actualSharingPercentage}%)`;
					// 未来可按需添加 '超额盈利', '用户回款' 等信息
				}

				// 只更新备注，不更新状态
				await updateChannelTransactionRemarks(
					channelExecuteTransactionId,
					remarksDetail,
					true,
				);
			}
		} catch (updateError) {
			// 记录错误但不影响主流程
			logger.error(
				{
					trade_no: executedOrderResult.trade_no,
					user_id: executedOrderResult.user_id,
					error:
						updateError instanceof Error
							? updateError.message
							: String(updateError),
				},
				"更新用户结算订单平台交易备注失败 - 需要手动处理",
			);
			// Do not re-throw, let the main flow complete if the core transaction was okay
		}

		return executedOrderResult;
	} catch (error) {
		logger.error(
			{
				error: error instanceof Error ? error.message : String(error),
				sellRequest,
			},
			"订单结算失败 - 可能需要手动处理",
		);
		throw error;
	}
}

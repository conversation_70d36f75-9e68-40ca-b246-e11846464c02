#!/usr/bin/env tsx

/**
 * 运行命令：
 * pnpm tsx --tsconfig D:\ink-dev\packages\server\tsconfig.json D:\ink-dev\packages\server\src\queue\ink\lockDashboard.ts
 */

import { getChildLogger } from "@/utils/logger.js";
import { coordinationRedis } from "@/lib/redis.js";
import { REDIS_KEYS } from "./constants.js";
import { fileURLToPath } from "node:url";

/**
 * 直接运行时的注意事项：
 * 1. 确保 Redis 服务已启动且配置正确
 */

const logger = getChildLogger("LockDashboard");

/**
 * 锁状态仪表板
 * 提供实时的锁状态监控和可视化
 */
export class LockDashboard {
	private isRunning = false;
	private refreshInterval = 2000; // 2秒刷新
	private lockHistory: Map<
		string,
		Array<{
			timestamp: number;
			holder: string;
			action: "acquired" | "released";
		}>
	> = new Map();

	/**
	 * 启动实时仪表板
	 */
	async start(): Promise<void> {
		this.isRunning = true;
		logger.info("锁状态仪表板启动");

		// 清屏并隐藏光标
		process.stdout.write("\x1Bc"); // 清屏
		process.stdout.write("\x1B[?25l"); // 隐藏光标

		// 监听Ctrl+C退出
		process.on("SIGINT", () => {
			this.stop();
		});

		while (this.isRunning) {
			try {
				await this.renderDashboard();
				await new Promise((resolve) =>
					setTimeout(resolve, this.refreshInterval),
				);
			} catch (error) {
				logger.error(error, "仪表板渲染错误");
				await new Promise((resolve) =>
					setTimeout(resolve, this.refreshInterval),
				);
			}
		}
	}

	/**
	 * 停止仪表板
	 */
	stop(): void {
		this.isRunning = false;
		process.stdout.write("\x1B[?25h"); // 显示光标
		console.log("\n仪表板已停止");
		process.exit(0);
	}

	/**
	 * 渲染仪表板
	 */
	private async renderDashboard(): Promise<void> {
		// 移动光标到顶部
		process.stdout.write("\x1B[H");

		const now = new Date();
		const output = [];

		// 标题栏
		output.push(`╔${"═".repeat(78)}╗`);
		output.push(
			`║ INK分布式锁监控仪表板 ${" ".repeat(30)} ${now.toLocaleString("zh-CN")} ║`,
		);
		output.push(`╠${"═".repeat(78)}╣`);

		// 获取所有锁状态
		const lockStates = await this.getAllLockStates();

		// 锁状态表格
		output.push(
			"║ 锁名称              │ 状态   │ 持有者        │ 剩余时间 │ 持有时长 ║",
		);
		output.push(`╠${"═".repeat(78)}╣`);

		for (const lock of lockStates) {
			const name = this.truncateString(lock.name, 18);
			const status = lock.isLocked ? "🔒 已锁定" : "🔓 空闲  ";
			const holder = this.truncateString(lock.holder || "-", 12);
			const ttl = lock.ttl ? `${lock.ttl}s` : "-";
			const duration = lock.holdDuration ? `${lock.holdDuration}s` : "-";

			const row = `║ ${name.padEnd(18)} │ ${status} │ ${holder.padEnd(12)} │ ${ttl.padEnd(8)} │ ${duration.padEnd(8)} ║`;
			output.push(row);
		}

		// 统计信息
		output.push(`╠${"═".repeat(78)}╣`);
		const totalLocks = lockStates.length;
		const activeLocks = lockStates.filter((l) => l.isLocked).length;
		const lockUtilization =
			totalLocks > 0 ? ((activeLocks / totalLocks) * 100).toFixed(1) : "0.0";

		output.push(
			`║ 锁统计: 总数 ${totalLocks} │ 活跃 ${activeLocks} │ 利用率 ${lockUtilization}% ${" ".repeat(25)} ║`,
		);

		// 最近活动
		output.push(`╠${"═".repeat(78)}╣`);
		output.push(
			"║ 最近锁活动 (最近10条)                                          ║",
		);
		output.push(`╠${"═".repeat(78)}╣`);

		const recentActivities = this.getRecentActivities(10);
		if (recentActivities.length === 0) {
			output.push(
				"║ 暂无活动记录                                                  ║",
			);
		} else {
			for (const activity of recentActivities) {
				const time = new Date(activity.timestamp).toLocaleTimeString();
				const action = activity.action === "acquired" ? "获取" : "释放";
				const lockName = this.truncateString(activity.lock, 20);
				const holder = this.truncateString(activity.holder, 15);

				const row = `║ ${time} ${action} ${lockName.padEnd(20)} by ${holder.padEnd(15)} ║`;
				output.push(row);
			}
		}

		// 健康状态
		output.push(`╠${"═".repeat(78)}╣`);
		const healthStatus = await this.getHealthStatus();
		const healthIcon = healthStatus.healthy ? "🟢" : "🔴";
		output.push(
			`║ 系统健康: ${healthIcon} ${healthStatus.status} ${" ".repeat(45)} ║`,
		);

		if (healthStatus.warnings.length > 0) {
			for (const warning of healthStatus.warnings.slice(0, 2)) {
				// 最多显示2个警告
				const warningText = this.truncateString(warning, 70);
				output.push(`║ ⚠️  ${warningText.padEnd(70)} ║`);
			}
		}

		// 底部
		output.push(`╠${"═".repeat(78)}╣`);
		output.push(
			"║ 按 Ctrl+C 退出仪表板                                          ║",
		);
		output.push(`╚${"═".repeat(78)}╝`);

		// 清空并输出
		process.stdout.write("\x1B[2J"); // 清屏
		process.stdout.write("\x1B[H"); // 移动到顶部
		console.log(output.join("\n"));

		// 更新活动历史
		await this.updateActivityHistory(lockStates);
	}

	/**
	 * 获取所有锁状态
	 */
	private async getAllLockStates(): Promise<
		Array<{
			name: string;
			isLocked: boolean;
			holder?: string;
			ttl?: number;
			holdDuration?: number;
		}>
	> {
		const locks = [
			{ key: REDIS_KEYS.INK_DATA_UPDATE_LOCK, name: "INK数据更新" },
			{ key: REDIS_KEYS.INK_QUOTES_UPDATE_LOCK, name: "价格报价更新" },
			{ key: REDIS_KEYS.INK_CONFIG_UPDATE_LOCK, name: "业务配置更新" },
			{ key: REDIS_KEYS.INK_SYNC_LOCK, name: "同步初始化" },
		];

		const results = [];

		for (const lock of locks) {
			try {
				const lockValue = await coordinationRedis.get(lock.key);
				const ttl = await coordinationRedis.ttl(lock.key);

				if (lockValue) {
					const [holderId, timestamp] = lockValue.split(":");
					const holdDuration =
						Math.floor(Date.now() / 1000) - Number.parseInt(timestamp);

					results.push({
						name: lock.name,
						isLocked: true,
						holder: holderId,
						ttl: ttl > 0 ? ttl : undefined,
						holdDuration,
					});
				} else {
					results.push({
						name: lock.name,
						isLocked: false,
					});
				}
			} catch (error) {
				results.push({
					name: lock.name,
					isLocked: false,
				});
			}
		}

		return results;
	}

	/**
	 * 更新活动历史
	 */
	private async updateActivityHistory(
		currentStates: Array<{ name: string; isLocked: boolean; holder?: string }>,
	): Promise<void> {
		const now = Date.now();

		for (const state of currentStates) {
			const history = this.lockHistory.get(state.name) || [];
			const lastRecord = history[history.length - 1];

			// 检查状态变化
			if (!lastRecord) {
				// 第一次记录
				if (state.isLocked && state.holder) {
					history.push({
						timestamp: now,
						holder: state.holder,
						action: "acquired",
					});
				}
			} else {
				// 检查状态变化
				if (
					state.isLocked &&
					state.holder &&
					lastRecord.action === "released"
				) {
					// 锁被获取
					history.push({
						timestamp: now,
						holder: state.holder,
						action: "acquired",
					});
				} else if (!state.isLocked && lastRecord.action === "acquired") {
					// 锁被释放
					history.push({
						timestamp: now,
						holder: lastRecord.holder,
						action: "released",
					});
				}
			}

			// 保持历史记录在合理范围内（最多100条）
			if (history.length > 100) {
				history.splice(0, history.length - 100);
			}

			this.lockHistory.set(state.name, history);
		}
	}

	/**
	 * 获取最近活动
	 */
	private getRecentActivities(limit: number): Array<{
		timestamp: number;
		lock: string;
		holder: string;
		action: "acquired" | "released";
	}> {
		const allActivities = [];

		for (const [lockName, history] of this.lockHistory.entries()) {
			for (const activity of history) {
				allActivities.push({
					timestamp: activity.timestamp,
					lock: lockName,
					holder: activity.holder,
					action: activity.action,
				});
			}
		}

		return allActivities
			.sort((a, b) => b.timestamp - a.timestamp)
			.slice(0, limit);
	}

	/**
	 * 获取健康状态
	 */
	private async getHealthStatus(): Promise<{
		healthy: boolean;
		status: string;
		warnings: string[];
	}> {
		const warnings = [];
		let healthy = true;
		let status = "系统正常";

		try {
			// 检查Redis连接
			const pingResult = await coordinationRedis.ping();
			if (pingResult !== "PONG") {
				healthy = false;
				status = "Redis连接异常";
				warnings.push("Redis连接失败");
			}

			// 检查是否有长时间持有的锁
			const lockStates = await this.getAllLockStates();
			const longHeldLocks = lockStates.filter(
				(lock) => lock.isLocked && lock.holdDuration && lock.holdDuration > 300, // 5分钟
			);

			if (longHeldLocks.length > 0) {
				warnings.push(`发现${longHeldLocks.length}个长时间持有的锁`);
			}

			// 检查锁利用率
			const activeLocks = lockStates.filter((l) => l.isLocked).length;
			const utilization = activeLocks / lockStates.length;

			if (utilization > 0.8) {
				warnings.push("锁利用率过高，可能存在性能问题");
			}
		} catch (error) {
			healthy = false;
			status = "健康检查失败";
			warnings.push("系统健康检查出错");
		}

		return { healthy, status, warnings };
	}

	/**
	 * 截断字符串
	 */
	private truncateString(str: string, maxLength: number): string {
		if (str.length <= maxLength) {
			return str;
		}
		return `${str.substring(0, maxLength - 3)}...`;
	}
}

// 命令行入口
if (process.argv[1] === fileURLToPath(import.meta.url)) {
	const dashboard = new LockDashboard();
	dashboard.start().catch((error) => {
		console.error("仪表板启动失败:", error);
		process.exit(1);
	});
}

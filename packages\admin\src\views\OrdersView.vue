<template>
	<div class="orders-view view">
		<div class="header-container">
			<h2>订单管理</h2>
		</div>

		<el-tabs v-model="activeTab">
			<el-tab-pane label="订单" name="orders">
				<div class="tab-header">
					<div class="search-bar">
						<el-input v-model="searchQuery" placeholder="输入用户ID搜索" class="search-input" clearable
							@keydown.enter="handleSearch" @clear="handleClear">
							<template #prefix>
								<el-icon>
									<Search />
								</el-icon>
							</template>
						</el-input>
					</div>
					<div class="controls">
						<!-- 日期范围选择器 -->
						<el-date-picker v-model="dateRange" type="daterange" class="date-range-picker" range-separator="至"
							start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD"
							@change="handleDateChange"></el-date-picker>

						<!-- 状态筛选下拉框 -->
						<el-select v-model="orderStatus" placeholder="订单状态" clearable @change="handleOrderStatusChange"
							class="filter-select">
							<el-option label="全部订单" value="all"></el-option>
							<el-option label="持仓中" value="holding"></el-option>
							<el-option label="已结算" value="sold"></el-option>
						</el-select>

						<el-button :loading="exporting" @click="exportData">
							<el-icon>
								<Download />
							</el-icon>
							导出数据
						</el-button>
					</div>
				</div>
				<div class="table-container"
					:class="{ 'show-left-shadow': showLeftShadow, 'show-right-shadow': showRightShadow }">
					<el-table ref="ordersTableRef" :data="orders" style="width: 100%" @sort-change="handleSortChange"
						@scroll="handleTableScroll">
						<el-table-column prop="trade_no" label="单号" min-width="140" sortable="custom" />
						<el-table-column prop="user_id" label="用户ID" min-width="108" sortable="custom" />
						<el-table-column prop="ts_code" label="标的" min-width="100" sortable="custom" />
						<el-table-column prop="structure" label="结构" min-width="100" sortable="custom">
							<template #default="{ row }">
								{{ formatStructure(row.structure) }}
							</template>
						</el-table-column>
						<el-table-column prop="term" label="期限" min-width="100" sortable="custom">
							<template #default="{ row }">
								{{ row.term === 14 ? '2周' : row.term + '个月' }}
							</template>
						</el-table-column>
						<el-table-column prop="expiry_date" label="到期日" min-width="120" sortable="custom">
							<template #default="{ row }">
								{{ formatDateDay(row.expiry_date) }}
							</template>
						</el-table-column>
						<el-table-column prop="entry_price" label="价格" min-width="280" sortable="custom">
							<template #default="{ row }">
								<div class="price-group">
									<el-tag>开仓: {{ formatPrice(row.entry_price) }}</el-tag>
									<el-tag type="warning">执行: {{ formatPrice(row.exercise_price) }}</el-tag>
									<el-tag type="info">结算: {{ formatPrice(row.settle_price) }}</el-tag>
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="total_scale" label="规模" min-width="180" sortable="custom">
							<template #default="{ row }">
								<div class="scale-group">
									<el-tag>总规模: {{ row.total_scale }}万</el-tag>
									<el-tag type="success">当前: {{ row.scale }}万</el-tag>
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="quote" label="期权费率" min-width="120" sortable="custom">
							<template #default="{ row }">
								<div class="quote-info">
									<span>{{ row.quote }}%</span>
									<span class="quote-diff"
										v-if="authStore.app_type === AppType.CHANNEL && row.quote_diff !== undefined && row.quote_diff !== null">
										({{ row.quote_diff >= 0 ? '+' : '' }}{{ row.quote_diff }}%)
									</span>
								</div>
							</template>
						</el-table-column>
						<el-table-column prop="quote_provider" label="交易方" min-width="100">
							<template #default="{ row }">
								{{ formatProvider(row.quote_provider) }}
							</template>
						</el-table-column>
						<el-table-column prop="status" label="状态" min-width="120" sortable="custom">
							<template #default="{ row }">
								<el-tag :type="getStatusType(row.status)">
									{{ formatOrderStatus(row.status) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column prop="created_at" label="创建时间" min-width="160" sortable="custom">
							<template #default="{ row }">
								{{ formatDate(row.created_at) }}
							</template>
						</el-table-column>
						<!-- 结算时间列，仅在已结算状态时显示 -->
						<el-table-column v-if="orderStatus === 'sold'" prop="closed_at" label="结算时间" min-width="160"
							sortable="custom">
							<template #default="{ row }">
								{{ formatDate(row.closed_at) }}
							</template>
						</el-table-column>
					</el-table>
				</div>

				<div class="pagination-container">
					<el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
						:total="total" layout="total, sizes, prev, pager, next" @size-change="handleSizeChange"
						@current-change="handleCurrentChange" />
				</div>
			</el-tab-pane>

			<el-tab-pane label="持仓" name="positions">
				<div class="tab-header">
					<div class="search-bar">
						<el-input v-model="searchQuery" placeholder="输入用户ID搜索" class="search-input" clearable
							@keydown.enter="handleSearch" @clear="handleClear">
							<template #prefix>
								<el-icon>
									<Search />
								</el-icon>
							</template>
						</el-input>
					</div>
					<div class="controls">
						<el-button :loading="exportingPositions" @click="exportPositionsData">
							<el-icon>
								<Download />
							</el-icon>
							导出数据
						</el-button>
					</div>
				</div>
				<el-table :data="positions" style="width: 100%">
					<el-table-column prop="trade_no" label="单号" min-width="120" />
					<el-table-column prop="user_id" label="用户ID" min-width="80" />
					<el-table-column prop="ts_code" label="标的" min-width="80" />
					<el-table-column label="价格" min-width="200">
						<template #default="{ row }">
							<div class="price-group">
								<el-tag>开仓: {{ formatPrice(row.entry_price) }}</el-tag>
								<el-tag type="warning">执行: {{ formatPrice(row.exercise_price) }}</el-tag>
								<el-tag type="info">市价: {{ getCurrentPrice(row.ts_code)?.toFixed(2) || '-' }}</el-tag>
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="scale" label="规模" min-width="80">
						<template #default="{ row }">
							{{ row.scale }}万
						</template>
					</el-table-column>
					<el-table-column prop="term" label="期限" min-width="80">
						<template #default="{ row }">
							{{ row.term === 14 ? '2周' : row.term + '个月' }}
						</template>
					</el-table-column>
					<el-table-column prop="quote" label="报价" min-width="100">
						<template #default="{ row }">
							<div class="quote-info">
								<span>{{ formatPrice(row.quote) }}%</span>
								<span class="quote-diff"
									v-if="authStore.app_type === AppType.CHANNEL && row.quote_diff !== undefined && row.quote_diff !== null">
									({{ row.quote_diff >= 0 ? '+' : '' }}{{ row.quote_diff }}%)
								</span>
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="quote_provider" label="交易方" min-width="80">
						<template #default="{ row }">
							{{ formatProvider(row.quote_provider) }}
						</template>
					</el-table-column>
					<el-table-column label="预估收益" min-width="100">
						<template #default="{ row }">
							<div :class="getProfitClass(calculateEstimatedValue(row))">
								{{ formatIntNumber(calculateEstimatedValue(row)) }}
							</div>
						</template>
					</el-table-column>
				</el-table>

				<div class="pagination-container">
					<el-pagination v-model:current-page="positionsCurrentPage" v-model:page-size="positionsPageSize"
						:page-sizes="[10, 20, 50, 100]" :total="positionsTotal" layout="total, sizes, prev, pager, next"
						@size-change="handlePositionsSizeChange" @current-change="handlePositionsCurrentChange" />
				</div>
			</el-tab-pane>

			<el-tab-pane label="挂单" name="pending-orders">
				<div class="tab-header">
					<div class="search-bar">
						<el-input v-model="pendingSearchQuery" placeholder="输入用户ID或标的搜索" class="search-input" clearable
							@keydown.enter="handlePendingSearch" @clear="handlePendingClear">
							<template #prefix>
								<el-icon>
									<Search />
								</el-icon>
							</template>
						</el-input>
					</div>
					<div class="controls">
						<el-select v-model="pendingOrderType" placeholder="订单类型" clearable @change="handlePendingOrderTypeChange">
							<el-option label="所有类型" value="" />
							<el-option label="限价订单" value="LIMIT" />
							<el-option label="均价订单" value="VWAP" />
						</el-select>
					</div>
				</div>
				<el-table :data="pendingOrders" style="width: 100%" v-loading="pendingLoading">
					<el-table-column prop="pending_id" label="ID" min-width="70" />
					<el-table-column prop="user_id" label="用户ID" min-width="80" />
					<el-table-column prop="ts_code" label="标的" min-width="100" />
					<el-table-column prop="entry_price" label="开仓价" min-width="100">
						<template #default="{ row }">
							{{ row.entry_price ? row.entry_price.toFixed(2) : '-' }}
						</template>
					</el-table-column>
					<el-table-column prop="scale" label="规模" min-width="100">
						<template #default="{ row }">
							{{ row.scale ? `${row.scale}万` : '-' }}
						</template>
					</el-table-column>
					<el-table-column prop="status" label="类型" min-width="80">
						<template #default="{ row }">
							<el-tag type="info">
								{{ getPendingType(row.status) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="status" label="方向" min-width="80">
						<template #default="{ row }">
							<el-tag :type="getPendingDirectionType(row.status).type">
								{{ getPendingDirectionType(row.status).text }}
							</el-tag>
						</template>
					</el-table-column>
					<!-- <el-table-column prop="status" label="状态" min-width="80">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ formatOrderStatus(row.status) }}
              </el-tag>
            </template>
          </el-table-column> -->
					<el-table-column prop="created_at" label="创建时间" min-width="160">
						<template #default="{ row }">
							{{ formatDate(row.created_at) }}
						</template>
					</el-table-column>
				</el-table>

				<div class="pagination-container">
					<el-pagination v-model:current-page="pendingCurrentPage" v-model:page-size="pendingPageSize"
						:page-sizes="[10, 20, 50, 100]" :total="pendingTotal" layout="total, sizes, prev, pager, next"
						@size-change="handlePendingSizeChange" @current-change="handlePendingCurrentChange" />
				</div>
			</el-tab-pane>

			<el-tab-pane v-if="hasOrderManagePermission" label="增改记录" name="order-management">
				<OrderManagementPanel />
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted, watch, nextTick } from "vue";
import { Search, Download } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { basicApi, type PendingOrderQueryOptions } from "@/api";
import { OrderStatus, AdminPermission, AppType } from "@packages/shared";
import type {
	OrderData,
	PositionData,
	PendingOrderData,
} from "@packages/shared";
import {
	formatDate,
	formatNumber,
	formatPrice,
	formatStructure,
	formatDateDay,
	formatIntNumber,
} from "@/utils/format";
import { exportToCsv } from "@/utils/export";
import OrderManagementPanel from "./panels/OrderManagementPanel.vue";
import { useAuthStore } from "@/stores/auth";
import { fetchCurrentPrices } from "@/utils/stock";
import { useSiteConfigStore } from "@/stores/siteConfig";

const siteConfigStore = useSiteConfigStore();

const authStore = useAuthStore();

const hasOrderManagePermission = computed(() => {
	return authStore.permissions.includes(AdminPermission.ORDER_MANAGE);
});

const activeTab = ref("orders");
const searchQuery = ref("");
const orders = ref<OrderData[]>([]);
const positions = ref<PositionData[]>([]);

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const positionsCurrentPage = ref(1);
const positionsPageSize = ref(10);
const positionsTotal = ref(0);

const sortBy = ref("created_at");
const sortOrder = ref<"DESC" | "ASC" | undefined>("DESC");

// 筛选条件
const orderStatus = ref("all");
const dateRange = ref<[Date, Date] | null>(null);
const startDate = ref("");
const endDate = ref("");

const statusTypes: Record<OrderStatus, string> = {
	[OrderStatus.HOLDING]: "success",
	[OrderStatus.SOLD]: "info",
	[OrderStatus.LIMIT_BUYING]: "warning",
	[OrderStatus.LIMIT_SELLING]: "warning",
	[OrderStatus.VWAP_BUYING]: "warning",
	[OrderStatus.VWAP_SELLING]: "warning",
};

const getStatusType = (status: OrderStatus) => statusTypes[status] || "info";
const formatOrderStatus = (status: OrderStatus) => {
	const statusMap: Record<OrderStatus, string> = {
		[OrderStatus.HOLDING]: "持仓中",
		[OrderStatus.SOLD]: "已结算",
		[OrderStatus.LIMIT_BUYING]: "限价待购",
		[OrderStatus.LIMIT_SELLING]: "限价待沽",
		[OrderStatus.VWAP_BUYING]: "VWAP待购",
		[OrderStatus.VWAP_SELLING]: "VWAP待沽",
	};
	return statusMap[status] || status;
};

const exporting = ref(false);
const exportingPositions = ref(false);

const currentPrices = ref<Map<string, number>>(new Map());
let pricePollingTimer: number | null = null;
const INTERVAL_PRICE_POLLING = 5000;

// 订单表格阴影状态
const ordersTableRef = ref();
const showLeftShadow = ref(false);
const showRightShadow = ref(false);

const getCurrentPrice = (tsCode: string): number | undefined => {
	return currentPrices.value.get(tsCode);
};

const calculateEstimatedValue = (position: PositionData): number => {
	const currentPrice = getCurrentPrice(position.ts_code);
	if (!currentPrice) return 0;

	const isCall = position.structure.endsWith("C");

	if (isCall) {
		if (currentPrice <= position.exercise_price) return 0;
		return (
			(position.scale * 10000 * (currentPrice - position.exercise_price)) /
			position.entry_price
		);
	}

	if (currentPrice >= position.exercise_price) return 0;
	return (
		(position.scale * 10000 * (position.exercise_price - currentPrice)) /
		position.entry_price
	);
};

const getProfitClass = (value: number | string): string => {
	const numValue = typeof value === "string" ? Number.parseFloat(value) : value;
	if (Number.isNaN(numValue) || value === "-") return "";
	return numValue > 0 ? "profit-positive" : "";
};

// 检测表格滚动状态
const checkTableShadows = () => {
	if (!ordersTableRef.value) return;

	const scrollWrapEl = ordersTableRef.value.$el.querySelector('.el-table__body-wrapper .el-scrollbar__wrap');
	if (!scrollWrapEl) return;

	const { scrollLeft, scrollWidth, clientWidth } = scrollWrapEl;

	showLeftShadow.value = scrollLeft > 0;
	showRightShadow.value = scrollLeft < scrollWidth - clientWidth - 1;
};

// 处理表格滚动事件
const handleTableScroll = () => {
	checkTableShadows();
};

const updateCurrentPrices = async () => {
	if (positions.value.length === 0) return;

	try {
		const codes = positions.value.map((position) => position.ts_code);
		const prices = await fetchCurrentPrices(codes);

		prices.forEach((price, index) => {
			currentPrices.value.set(codes[index], price);
		});
	} catch (error) {
		console.error("Failed to fetch current prices:", error);
	}
};

const setupPricePolling = () => {
	if (pricePollingTimer) {
		clearInterval(pricePollingTimer);
	}

	updateCurrentPrices();

	pricePollingTimer = window.setInterval(
		updateCurrentPrices,
		INTERVAL_PRICE_POLLING,
	);
};

watch(
	() => activeTab.value,
	async (newTab) => {
		if (newTab === "positions") {
			setupPricePolling();
		} else if (newTab === "pending-orders") {
			loadPendingOrders();
		} else if (newTab === "orders") {
			await nextTick();
			checkTableShadows();
		} else {
			if (pricePollingTimer) {
				clearInterval(pricePollingTimer);
			}
		}
	},
);

watch(positions, () => {
	if (activeTab.value === "positions") {
		updateCurrentPrices();
	}
});

onUnmounted(() => {
	if (pricePollingTimer) {
		clearInterval(pricePollingTimer);
	}
	window.removeEventListener('resize', checkTableShadows);
});

const exportData = async () => {
	exporting.value = true;
	try {
		const response = await basicApi.getOrders(1, 999999, {
			sortBy: sortBy.value,
			sortOrder: sortOrder.value,
		});

		const headers = [
			"单号",
			"用户ID",
			"标的",
			"结构",
			"期限",
			"到期日",
			"开仓价",
			"执行价",
			"结算价",
			"总规模",
			"当前规模",
			"期权费率",
			"报价提供商",
			"状态",
			"创建时间",
			"关闭时间",
		];
		const csvData =
			response?.items.map((item) => [
				item.trade_no,
				item.user_id,
				item.ts_code,
				formatStructure(item.structure),
				item.term === 14 ? "2周" : `${item.term}个月`,
				formatDate(item.expiry_date),
				item.entry_price,
				item.exercise_price,
				item.settle_price || "",
				item.total_scale,
				item.scale,
				`${item.quote}%`,
				formatProvider(item.quote_provider),
				formatOrderStatus(item.status),
				formatDate(item.created_at),
				item.closed_at ? formatDate(item.closed_at) : "",
			]) || [];

		exportToCsv(headers, csvData, "orders");
		ElMessage.success("导出成功");
	} catch (error) {
		console.error("Export failed:", error);
		ElMessage.error("导出失败");
	} finally {
		exporting.value = false;
	}
};

const exportPositionsData = async () => {
	exportingPositions.value = true;
	try {
		const response = await basicApi.getPositions(1, 999999);

		const headers = [
			"单号",
			"用户ID",
			"标的",
			"开仓价",
			"执行价",
			"规模",
			"期限",
			"期权费率",
			"报价提供商",
		];
		const csvData =
			response?.items.map((item) => [
				item.trade_no,
				item.user_id,
				item.ts_code,
				item.entry_price,
				item.exercise_price,
				`${formatNumber(item.scale)}万`,
				item.term === 14 ? "2周" : `${item.term}个月`,
				`${item.quote}%`,
				formatProvider(item.quote_provider),
			]) || [];

		exportToCsv(headers, csvData, "positions");
		ElMessage.success("导出成功");
	} catch (error) {
		console.error("Export positions failed:", error);
		ElMessage.error("导出失败");
	} finally {
		exportingPositions.value = false;
	}
};

const loadData = async (userId?: number) => {
	try {
		// 构建查询参数
		const queryParams: {
			sortBy: string;
			sortOrder: "ASC" | "DESC";
			status?: string;
			startDate?: string;
			endDate?: string;
		} = {
			sortBy: sortBy.value,
			sortOrder: sortOrder.value as "ASC" | "DESC",
		};

		// 添加状态筛选
		if (orderStatus.value !== "all") {
			queryParams.status = orderStatus.value;
		}

		// 添加日期筛选
		if (startDate.value) {
			queryParams.startDate = startDate.value;
		}
		if (endDate.value) {
			queryParams.endDate = endDate.value;
		}

		const [ordersData, positionsData] = await Promise.all([
			userId
				? basicApi.getUserOrders(userId, currentPage.value, pageSize.value, queryParams)
				: basicApi.getOrders(currentPage.value, pageSize.value, queryParams),
			userId
				? basicApi.getUserPositions(
					userId,
					positionsCurrentPage.value,
					positionsPageSize.value,
				)
				: basicApi.getPositions(
					positionsCurrentPage.value,
					positionsPageSize.value,
				),
		]);
		orders.value = ordersData?.items || [];
		total.value = ordersData?.total || 0;
		positions.value = positionsData?.items || [];
		positionsTotal.value = positionsData?.total || 0;

		// 数据加载后检查表格阴影状态
		if (activeTab.value === 'orders') {
			await nextTick();
			checkTableShadows();
		}
	} catch (error) {
		console.error("Failed to fetch data:", error);
		ElMessage.error("获取数据失败");
	}
};

const handleSearch = async () => {
	if (!searchQuery.value.trim()) {
		await loadData();
		return;
	}
	const userId = Number.parseInt(searchQuery.value);
	if (Number.isNaN(userId)) {
		ElMessage.warning("Please enter a valid user ID");
		return;
	}
	await loadData(userId);
};

const handleClear = () => {
	loadData();
};

const handleSizeChange = (newSize: number) => {
	pageSize.value = newSize;
	loadData(Number.parseInt(searchQuery.value) || undefined);
};

const handleCurrentChange = (newPage: number) => {
	currentPage.value = newPage;
	loadData(Number.parseInt(searchQuery.value) || undefined);
};

const handlePositionsSizeChange = (newSize: number) => {
	positionsPageSize.value = newSize;
	loadData(Number.parseInt(searchQuery.value) || undefined);
};

const handlePositionsCurrentChange = (newPage: number) => {
	positionsCurrentPage.value = newPage;
	loadData(Number.parseInt(searchQuery.value) || undefined);
};

const handleSortChange = ({
	prop,
	order,
}: { prop?: string; order?: string }) => {
	sortBy.value = prop || "created_at";
	sortOrder.value = order === "ascending" ? "ASC" : "DESC";
	loadData(Number.parseInt(searchQuery.value) || undefined);
};

// 筛选器事件处理
const handleOrderStatusChange = () => {
	currentPage.value = 1; // 重置到第一页
	loadData(Number.parseInt(searchQuery.value) || undefined);
};

const handleDateChange = () => {
	if (dateRange.value) {
		startDate.value = dateRange.value[0].toISOString().split("T")[0];
		endDate.value = dateRange.value[1].toISOString().split("T")[0];
	} else {
		startDate.value = "";
		endDate.value = "";
	}

	currentPage.value = 1; // 重置到第一页
	loadData(Number.parseInt(searchQuery.value) || undefined);
};

const formatProvider = (provider?: string) => {
	if (!provider) return siteConfigStore.shortName();
	if (provider === "INK") return siteConfigStore.shortName();

	const providerMap: Record<string, string> = {
		HAIYING: "HY",
		YINHE_DERUI: "YHDR",
		ZHONGZHENG_ZIBEN: "ZZZB",
		ZHEQI: "ZQ",
		YONGAN: "YA",
		ZHONGJIN: "ZJ",
		GUANGFA: "GF",
		GUOJUNZI: "GJZ",
	};

	return providerMap[provider] || provider;
};

// 挂单查看相关状态
const pendingOrders = ref<PendingOrderData[]>([]);
const pendingSearchQuery = ref("");
const pendingOrderType = ref<"LIMIT" | "VWAP" | "">("");
const pendingLoading = ref(false);
const pendingCurrentPage = ref(1);
const pendingPageSize = ref(10);
const pendingTotal = ref(0);

// 加载挂单数据
const loadPendingOrders = async () => {
	pendingLoading.value = true;
	try {
		const options: PendingOrderQueryOptions = {};

		// 根据选择的订单类型过滤
		if (pendingOrderType.value) {
			const typeToStatus: Record<string, OrderStatus[]> = {
				LIMIT: [OrderStatus.LIMIT_BUYING, OrderStatus.LIMIT_SELLING],
				VWAP: [OrderStatus.VWAP_BUYING, OrderStatus.VWAP_SELLING],
			};
			options.types = typeToStatus[pendingOrderType.value];
		}

		// 处理搜索条件
		if (pendingSearchQuery.value) {
			if (/^\d+$/.test(pendingSearchQuery.value)) {
				// 如果是数字，则作为用户ID
				options.user_id = Number.parseInt(pendingSearchQuery.value);
			} else {
				// 否则作为标的代码
				options.ts_code = pendingSearchQuery.value;
			}
		}

		const result = await basicApi.getPendingOrders(
			pendingCurrentPage.value,
			pendingPageSize.value,
			options,
		);

		if (result) {
			pendingOrders.value = result.items;
			pendingTotal.value = result.total;
		} else {
			pendingOrders.value = [];
			pendingTotal.value = 0;
		}
	} catch (error) {
		console.error("Failed to fetch pending orders:", error);
		ElMessage.error("获取挂单数据失败");
	} finally {
		pendingLoading.value = false;
	}
};

// 处理挂单查询
const handlePendingSearch = () => {
	pendingCurrentPage.value = 1; // 重置到第一页
	loadPendingOrders();
};

const handlePendingClear = () => {
	pendingSearchQuery.value = "";
	loadPendingOrders();
};

const handlePendingOrderTypeChange = () => {
	pendingCurrentPage.value = 1; // 重置到第一页
	loadPendingOrders();
};

// 挂单分页处理
const handlePendingSizeChange = (newSize: number) => {
	pendingPageSize.value = newSize;
	loadPendingOrders();
};

const handlePendingCurrentChange = (newPage: number) => {
	pendingCurrentPage.value = newPage;
	loadPendingOrders();
};

// 获取挂单状态类型
const getPendingType = (status: OrderStatus): string => {
	const statusTypes: Record<string, string> = {
		[OrderStatus.LIMIT_BUYING]: "限价",
		[OrderStatus.LIMIT_SELLING]: "限价",
		[OrderStatus.VWAP_BUYING]: "均价",
		[OrderStatus.VWAP_SELLING]: "均价",
	};
	return statusTypes[status] || "未知";
};

// 获取挂单状态类型
const getPendingDirectionType = (
	status: OrderStatus,
): { type: string; text: string } => {
	const statusTypes: Record<string, { type: string; text: string }> = {
		[OrderStatus.LIMIT_BUYING]: { type: "success", text: "买入" },
		[OrderStatus.VWAP_BUYING]: { type: "success", text: "买入" },
		[OrderStatus.LIMIT_SELLING]: { type: "danger", text: "卖出" },
		[OrderStatus.VWAP_SELLING]: { type: "danger", text: "卖出" },
	};
	return statusTypes[status] || { type: "info", text: "未知" };
};

onMounted(() => {
	loadData();

	if (activeTab.value === "positions") {
		setupPricePolling();
	} else if (activeTab.value === "pending-orders") {
		loadPendingOrders();
	}

	// 监听窗口大小变化
	window.addEventListener('resize', checkTableShadows);
});
</script>

<style scoped>
h2 {
	margin: 0;
}

.tab-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	gap: 10px;
	flex-wrap: wrap;
}

.search-bar {
	flex: 1;
	max-width: 300px;
	min-width: 140px;
}

.controls {
	display: flex;
	gap: 10px;
	align-items: center;
	flex-wrap: wrap;
}

.controls :deep(.el-select) {
	width: 100px;
}

.filter-select {
	width: 120px;
}

:deep(.date-range-picker) {
	width: 240px;
}

.price-group,
.time-group {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
}

.scale-group {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
}

.quote-info {
	display: flex;
	gap: 4px;
	align-items: center;
}

.quote-diff {
	color: var(--el-text-color-secondary);
	font-size: 13px;
}

:deep(.el-table) {
	--el-table-bg-color: var(--el-bg-color-overlay);
	--el-table-tr-bg-color: var(--el-bg-color-overlay);
	--el-table-header-bg-color: var(--el-bg-color-overlay);
	--el-table-border-color: var(--el-border-color-lighter);

	background-color: var(--el-bg-color-overlay);
	color: var(--el-text-color-primary);
}

:deep(.el-table th) {
	background-color: var(--el-bg-color-overlay);
	color: var(--el-text-color-regular);
}

:deep(.el-table td) {
	color: var(--el-text-color-primary);
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
	background-color: var(--el-table-row-hover-bg-color);
}

:deep(.el-tabs__item) {
	color: var(--el-text-color-regular);
}

:deep(.el-tabs__item.is-active) {
	color: var(--el-color-primary);
}

:deep(.el-tabs__nav-wrap::after) {
	background-color: var(--el-border-color-light);
}

:deep(.el-input__wrapper) {
	background-color: var(--el-bg-color-overlay);
}

:deep(.el-input__inner) {
	color: var(--el-text-color-primary);
}

:deep(.el-input__prefix-icon) {
	color: var(--el-text-color-placeholder);
}

/* 国内股市红涨绿跌 */
.profit-positive {
	color: var(--el-color-error);
}

.profit-negative {
	color: var(--el-color-success);
}

/* 订单表格阴影效果 */
.table-container {
	position: relative;
}

.table-container::before,
.table-container::after {
	content: '';
	position: absolute;
	top: 0;
	bottom: 0;
	width: 20px;
	pointer-events: none;
	z-index: 10;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.table-container::before {
	left: 0;
	background: linear-gradient(to right, var(--el-border-color), transparent);
}

.table-container::after {
	right: 0;
	background: linear-gradient(to left, var(--el-border-color), transparent);
}

.table-container.show-left-shadow::before {
	opacity: 1;
}

.table-container.show-right-shadow::after {
	opacity: 1;
}
</style>

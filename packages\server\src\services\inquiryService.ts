import {
	fetchStockList,
	getExternalOptionQuotes,
} from "@/financeUtils/marketData.js";
import * as InquiryModel from "@/models/inquiry.js";
import { adjustQuoteBasedOnExternal } from "@/financeUtils/quote.js";
import logger from "@/utils/logger.js";
import { AppError } from "@/core/appError.js";
import type {
	SearchStocksResponse,
	QuoteRequest,
	QuoteResponse,
	InquiryData,
	PlatformConfig,
} from "@packages/shared";
import {
	InquiryStatus,
	QUOTE_REJECTED,
	INQUIRY_EXPIRATION_TIME_IN_SECONDS,
} from "@packages/shared";
import { appRedis } from "@/lib/redis.js";
import * as SystemAvailabilityManager from "@/core/systemAvailabilityManager.js";
import * as configService from "@/services/admin/configService.js";
import { APP_CONFIG, isChannel } from "@/config/configManager.js";
import * as User from "@/models/user.js";

const SEARCH_PAGE_SIZE = 20;

export async function searchStocks(
	query = "",
	offset = 0,
): Promise<SearchStocksResponse> {
	try {
		const stocks = await fetchStockList();
		const searchText = query.trim().toLowerCase();

		// 空查询返回所有结果
		const filtered =
			searchText === ""
				? stocks
				: stocks.filter(
						(stock) =>
							stock.ts_code.toLowerCase().includes(searchText) ||
							stock.name.toLowerCase().includes(searchText),
					);

		const startIndex = offset;
		const endIndex = startIndex + SEARCH_PAGE_SIZE;

		return {
			data: filtered.slice(startIndex, endIndex),
			total: filtered.length,
			hasMore: filtered.length > endIndex,
		};
	} catch (error) {
		logger.error(error, "Error searching stocks");
		throw AppError.create("STOCK_SEARCH_FAILED", "Failed to search stocks");
	}
}

export async function processBatchQuotes({
	quotes,
	user_id,
}: {
	quotes: QuoteRequest[];
	user_id: number;
}): Promise<QuoteResponse[]> {
	try {
		const platformConfig = await configService.getPlatformConfig();
		// 处理每个询价请求
		// Use Promise.all to wait for all async processQuote calls to complete
		// 保留 await 以等待目前函数 catch 块的错误处理
		return await Promise.all(
			quotes.map((quote) =>
				processQuote({ ...quote, user_id }, platformConfig),
			),
		);
	} catch (error) {
		logger.error(error, "Error processing batch quotes");
		throw AppError.create(
			"INQUIRY_PROCESS_FAILED",
			"Failed to process batch quotes",
		);
	}
}

export async function processQuote(
	{
		user_id,
		ts_code,
		structure,
		scale,
		term,
	}: QuoteRequest & {
		user_id: number;
	},
	platformConfigInput?: PlatformConfig,
	isTest = false,
): Promise<QuoteResponse> {
	const startTime = performance.now();
	try {
		logger.info(
			`Processing quote for user ${user_id}, ${ts_code}, structure: ${structure}, term: ${term}${isTest ? " (TEST MODE)" : ""}`,
		);

		const currentPlatformConfig =
			platformConfigInput ?? (await configService.getPlatformConfig());

		// 只有通道版本才需要获取用户自定义报价差异配置
		let userCustomQuoteDiffs = null;

		if (isChannel()) {
			const user = await User.findById(user_id);
			if (user) {
				userCustomQuoteDiffs = user.custom_quote_diffs;
			}
		}

		const checkScaleStart = performance.now();
		try {
			await SystemAvailabilityManager.checkStockScaleLimit(ts_code, scale);
			logger.info(
				`PERF: Stock scale check took ${performance.now() - checkScaleStart}ms for ${ts_code}`,
			);
		} catch (error) {
			logger.warn(
				`Scale limit check failed for ${ts_code}: ${error instanceof Error ? error.message : String(error)}`,
			);
			const inquiry = await InquiryModel.create({
				user_id,
				ts_code,
				scale,
				structure,
				term,
				quote: QUOTE_REJECTED,
				status: InquiryStatus.REJECTED,
				external_quotes: {},
				quote_diffs: {},
			});
			return inquiry;
		}

		const { calculatedQuote, externalQuotes, allExternalQuotes, quoteDiffs } =
			await getExternalOptionQuotes(
				ts_code,
				structure,
				term,
				currentPlatformConfig,
				scale,
				userCustomQuoteDiffs, // 只有通道版本才传递用户自定义报价差异
			);

		const finalAdjustedQuote = await adjustQuoteBasedOnExternal(
			calculatedQuote,
			allExternalQuotes,
		);

		const quoteToStore = finalAdjustedQuote ?? QUOTE_REJECTED;

		let status = InquiryStatus.PROCESSING;
		const hasValidExternalQuote = Object.values(externalQuotes).some(
			(price) => price !== null && !Number.isNaN(price) && price > 0,
		);

		if (quoteToStore === QUOTE_REJECTED && !hasValidExternalQuote) {
			status = InquiryStatus.REJECTED;
			logger.warn(
				`Rejecting inquiry for ${ts_code}, structure: ${structure}, term: ${term}. No valid quotes available.`,
				{ calculatedQuote, finalAdjustedQuote, externalQuotes },
			);
		} else if (quoteToStore === QUOTE_REJECTED && hasValidExternalQuote) {
			logger.info(
				`Internal quote rejected for ${ts_code}, but external quotes available. Status: PROCESSING.`,
				{ calculatedQuote, finalAdjustedQuote, externalQuotes },
			);
		}

		// 如果是测试模式，则不创建实际询价记录，而是构造一个模拟的 Inquiry 对象
		if (isTest) {
			const mockInquiry: QuoteResponse = {
				// inquiry_id 通常由数据库生成，测试模式下可以设为特定值或 null
				// 这里我们暂时不设置 inquiry_id，或者设置为一个特殊值如 0 或 -1
				// 以表明这是一个未持久化的测试询价
				inquiry_id: 0, // 或者 null, undefined，取决于下游如何处理
				user_id,
				ts_code,
				scale,
				structure,
				term,
				quote: quoteToStore,
				status,
				external_quotes: externalQuotes,
				quote_diffs: quoteDiffs,
				created_at: new Date().toISOString(), // 模拟创建时间
				// 其他 InquiryData 中可能需要的字段可以赋默认值或模拟值
			};
			const endTime = performance.now();
			logger.info(
				`PERF: Total quote processing for ${ts_code} (TEST MODE) took ${endTime - startTime}ms`,
			);
			return mockInquiry;
		}

		// 非测试模式，正常创建询价
		const inquiry = await InquiryModel.create({
			user_id,
			ts_code,
			scale,
			structure,
			term,
			quote: quoteToStore,
			status,
			external_quotes: externalQuotes,
			quote_diffs: quoteDiffs, // This should be the effective diffs applied
		});

		if (status === InquiryStatus.PROCESSING) {
			// 已经确保 !isTest
			await appRedis.setex(
				`inquiry:${inquiry.inquiry_id}`,
				INQUIRY_EXPIRATION_TIME_IN_SECONDS,
				JSON.stringify(inquiry),
			);
		}

		const endTime = performance.now();
		logger.info(
			`PERF: Total quote processing for ${ts_code} took ${endTime - startTime}ms`,
		);
		return inquiry;
	} catch (error) {
		const endTime = performance.now();
		logger.error(
			error,
			`Error processing quote for ${ts_code} (took ${endTime - startTime}ms)`,
		);
		throw AppError.create(
			"INQUIRY_PROCESS_FAILED",
			"Failed to process inquiry",
			{ ts_code },
		);
	}
}

/**
 * 获取用户查询记录
 */
export async function getUserInquiries(
	user_id: number,
	page: number,
	pageSize: number,
	isDescending: boolean,
	filters?: {
		ts_codes?: string[];
	},
): Promise<{
	items: InquiryData[];
	total: number;
	ts_codes: string[];
}> {
	const offset = (page - 1) * pageSize;

	// 获取总数和分页数据
	const [total, inquiries, ts_codes] = await Promise.all([
		InquiryModel.countByUserId(user_id, filters),
		InquiryModel.getByUserId(user_id, offset, pageSize, isDescending, filters),
		InquiryModel.getAllTsCodes(user_id),
	]);

	// 处理每个询价的状态
	for (const inquiry of inquiries) {
		if (inquiry.status === "processing") {
			const inquiryKey = `inquiry:${inquiry.inquiry_id}`;
			const exists = await appRedis.exists(inquiryKey);

			if (!exists) {
				try {
					await InquiryModel.updateStatus(
						inquiry.inquiry_id,
						InquiryStatus.EXPIRED,
					);
					inquiry.status = InquiryStatus.EXPIRED;
				} catch (error) {
					logger.error(error, "Failed to update expired inquiry status");
				}
			}
		}
	}

	return {
		total,
		items: inquiries,
		ts_codes,
	};
}

/**
 * 管理员获取询价记录，并处理过期状态
 */
export async function getAdminInquiries(
	options: InquiryModel.InquiryQueryOptions = {},
): Promise<{
	total: number;
	items: InquiryData[];
}> {
	// 获取询价记录
	const result = await InquiryModel.getAll(options);

	// 处理每个询价的状态
	for (const inquiry of result.items) {
		if (inquiry.status === InquiryStatus.PROCESSING) {
			const inquiryKey = `inquiry:${inquiry.inquiry_id}`;
			const exists = await appRedis.exists(inquiryKey);

			if (!exists) {
				try {
					await InquiryModel.updateStatus(
						inquiry.inquiry_id,
						InquiryStatus.EXPIRED,
					);
					inquiry.status = InquiryStatus.EXPIRED;
				} catch (error) {
					logger.error(error, "Failed to update expired inquiry status");
				}
			}
		}
	}

	return result;
}

/**
 * 获取所有股票数据
 * @returns 所有股票列表
 */
export async function getBasicStocks() {
	try {
		const stocks = await fetchStockList();
		return stocks;
	} catch (error) {
		logger.error(error, "Error fetching all stocks");
		throw AppError.create("STOCK_SEARCH_FAILED", "Failed to fetch all stocks");
	}
}

/**
 * 获取所有询价记录，返回文本格式
 * 格式：inquiry_id~user_id~ts_code~scale~term~structure~quote~status~created_at~app_id;...
 */
export async function getAllTextByApi(): Promise<string> {
	// 获取所有询价记录
	const inquiries = await InquiryModel.getAllByApi();

	// 处理每个询价的状态
	for (const inquiry of inquiries) {
		if (inquiry.status === InquiryStatus.PROCESSING) {
			const inquiryKey = `inquiry:${inquiry.inquiry_id}`;
			const exists = await appRedis.exists(inquiryKey);

			if (!exists) {
				try {
					await InquiryModel.updateStatus(
						inquiry.inquiry_id,
						InquiryStatus.EXPIRED,
					);
					inquiry.status = InquiryStatus.EXPIRED;
				} catch (error) {
					logger.error(error, "Failed to update expired inquiry status");
				}
			}
		}
	}

	// 构建数据字符串
	return inquiries
		.map((inquiry) => {
			const values = [
				inquiry.inquiry_id?.toString() || "",
				inquiry.user_id?.toString() || "",
				inquiry.ts_code || "",
				inquiry.scale?.toString() || "0",
				inquiry.term?.toString() || "0",
				inquiry.structure || "",
				inquiry.quote?.toString() || "0",
				inquiry.status || "",
				inquiry.created_at || "",
				APP_CONFIG.tradingPlatformId || APP_CONFIG.channelId || "",
			].join("~");

			return values;
		})
		.join(";\n");
}

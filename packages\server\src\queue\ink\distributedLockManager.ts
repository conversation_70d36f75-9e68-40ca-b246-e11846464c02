import { coordinationRedis } from "@/lib/redis.js";
import { getChildLogger } from "@/utils/logger.js";
import { LOCK_CONFIG } from "./constants.js";
import { APP_CONFIG } from "@/config/configManager.js";

/**
 * 分布式锁管理器
 *
 * 核心功能：
 * - 提供带自动续期的分布式锁功能
 * - 防止多实例环境下的并发冲突
 * - 支持锁的自动续期和优雅释放
 * - 提供锁状态监控和管理
 *
 * 设计特点：
 * - 基于Redis的SET NX EX命令实现原子性锁获取
 * - 自动续期机制防止锁意外过期
 * - 应用实例标识确保锁的正确归属
 * - 优雅关闭时自动清理所有锁
 *
 * 使用场景：
 * - 防止定时任务重复执行
 * - 保护共享资源的并发访问
 * - 确保关键操作的原子性
 */
export class DistributedLockManager {
	private renewalTimers: Map<string, NodeJS.Timeout> = new Map();
	private lockLogger = getChildLogger("DistributedLockManager");

	/**
	 * 获取应用标识符
	 *
	 * 标识符生成策略：
	 * 1. 优先使用交易平台ID（platform:{id}）
	 * 2. 其次使用渠道ID（channel:{id}）
	 * 3. 最后生成随机ID（unknown:{random}）
	 *
	 * 用途：
	 * - 在分布式环境中唯一标识应用实例
	 * - 确保锁只能被创建它的实例释放
	 * - 便于问题排查和监控
	 *
	 * @returns 应用标识符
	 */
	private getAppIdentifier(): string {
		const platformId = APP_CONFIG.tradingPlatformId;
		const channelId = APP_CONFIG.channelId;

		// 确保标识符的一致性，避免空值导致的问题
		if (platformId && platformId.trim() !== "") {
			return `platform:${platformId.trim()}`;
		}
		if (channelId && channelId.trim() !== "") {
			return `channel:${channelId.trim()}`;
		}

		// 生成随机ID作为后备标识，但要记录错误
		const randomId = Math.random().toString(36).substring(2, 9);
		this.lockLogger.error(
			{ platformId, channelId },
			`No valid platform or channel ID found, using random identifier: unknown:${randomId}`,
		);
		return `unknown:${randomId}`;
	}

	/**
	 * 获取锁并自动续期
	 *
	 * 锁获取流程：
	 * 1. 生成包含应用ID和时间戳的锁值
	 * 2. 使用Redis SET NX EX命令原子性获取锁
	 * 3. 成功获取后启动自动续期定时器
	 * 4. 支持等待模式，可配置最大等待时间
	 *
	 * 锁值格式：{appId}:{timestamp}
	 *
	 * @param lockKey 锁的键名
	 * @param expiry 锁过期时间（秒）
	 * @param maxWaitTime 最大等待时间（毫秒），0表示不等待
	 * @returns 是否成功获取锁
	 */
	async acquireLockWithRenewal(
		lockKey: string,
		expiry: number = LOCK_CONFIG.EXPIRY,
		maxWaitTime = 0,
	): Promise<boolean> {
		const appId = this.getAppIdentifier();
		const startTime = Date.now();

		while (true) {
			const now = Math.floor(Date.now() / 1000);
			const lockValue = `${appId}:${now}`;

			try {
				// 使用Redis SET命令的原子性操作获取锁
				// NX: 只在键不存在时设置
				// EX: 设置过期时间（秒）
				const result = await coordinationRedis.set(
					lockKey,
					lockValue,
					"EX",
					expiry,
					"NX",
				);

				if (result === "OK") {
					this.lockLogger.info(`Lock acquired: ${lockKey} by ${appId}`);
					this.startLockRenewal(lockKey, lockValue, expiry);
					return true;
				}

				// 如果不等待或已超过最大等待时间，直接返回
				if (maxWaitTime === 0 || Date.now() - startTime >= maxWaitTime) {
					const existingLock = await coordinationRedis.get(lockKey);
					if (existingLock) {
						const [lockHolderId] = existingLock.split(":");
						this.lockLogger.info(
							{
								currentAppId: appId,
								existingLockValue: existingLock,
								lockKey,
								maxWaitTime,
							},
							`Failed to acquire lock: ${lockKey}, held by: ${lockHolderId}`,
						);
					}
					return false;
				}

				// 等待一小段时间后重试
				await new Promise((resolve) => setTimeout(resolve, 100));
			} catch (error) {
				this.lockLogger.error(error, `Error acquiring lock: ${lockKey}`);
				return false;
			}
		}
	}

	/**
	 * 释放锁并停止续期
	 *
	 * 释放流程：
	 * 1. 检查锁是否由当前实例持有
	 * 2. 只有锁的持有者才能释放锁
	 * 3. 删除Redis中的锁键
	 * 4. 停止自动续期定时器
	 *
	 * 安全性：
	 * - 通过锁值中的应用ID验证锁的归属
	 * - 防止误释放其他实例的锁
	 *
	 * @param lockKey 锁的键名
	 */
	async releaseLock(lockKey: string): Promise<void> {
		const appId = this.getAppIdentifier();

		try {
			const existingLock = await coordinationRedis.get(lockKey);

			if (!existingLock) {
				this.lockLogger.warn(
					{ appId, lockKey },
					`Attempted to release non-existent lock: ${lockKey}`,
				);
				this.stopLockRenewal(lockKey); // 清理可能的续期定时器
				return;
			}

			// 只有锁的持有者才能释放锁
			if (existingLock.startsWith(`${appId}:`)) {
				await coordinationRedis.del(lockKey);
				this.stopLockRenewal(lockKey);
				this.lockLogger.info(
					{ lockValue: existingLock },
					`Lock released: ${lockKey} by ${appId}`,
				);
			} else {
				const [lockHolderId] = existingLock.split(":");
				this.lockLogger.warn(
					{
						currentAppId: appId,
						lockHolderId,
						existingLockValue: existingLock,
						lockKey,
					},
					`Attempted to release lock not owned by this instance: ${lockKey}`,
				);
			}
		} catch (error) {
			this.lockLogger.error(
				{
					appId,
					lockKey,
					error,
				},
				`Error releasing lock: ${lockKey}`,
			);
		}
	}

	/**
	 * 检查锁是否被当前实例持有
	 *
	 * 检查方式：
	 * - 获取锁的当前值
	 * - 验证锁值是否以当前应用ID开头
	 *
	 * @param lockKey 锁的键名
	 * @returns 是否被当前实例持有
	 */
	async isLockHeldByCurrentInstance(lockKey: string): Promise<boolean> {
		try {
			const appId = this.getAppIdentifier();
			const existingLock = await coordinationRedis.get(lockKey);
			return existingLock?.startsWith(`${appId}:`) ?? false;
		} catch (error) {
			this.lockLogger.error(error, `Error checking lock ownership: ${lockKey}`);
			return false;
		}
	}

	/**
	 * 启动锁续期
	 *
	 * 续期机制：
	 * - 定期检查锁值是否仍然匹配
	 * - 匹配则延长锁的过期时间
	 * - 不匹配则停止续期（锁已被其他实例获取）
	 *
	 * 续期间隔：由LOCK_CONFIG.RENEWAL_INTERVAL配置
	 *
	 * @param lockKey 锁的键名
	 * @param lockValue 锁的值（用于验证归属）
	 * @param expiry 锁过期时间（秒）
	 */
	private startLockRenewal(
		lockKey: string,
		lockValue: string,
		expiry: number,
	): void {
		const timer = setInterval(async () => {
			try {
				const currentLock = await coordinationRedis.get(lockKey);
				if (currentLock === lockValue) {
					// 锁值匹配，延长过期时间
					await coordinationRedis.expire(lockKey, expiry);
					this.lockLogger.debug(`Lock renewed: ${lockKey}`);
				} else {
					// 锁值不匹配，可能已被其他实例获取
					this.lockLogger.warn(
						`Lock value changed, stopping renewal: ${lockKey}`,
					);
					this.stopLockRenewal(lockKey);
				}
			} catch (error) {
				this.lockLogger.error(error, `Failed to renew lock: ${lockKey}`);
				this.stopLockRenewal(lockKey);
			}
		}, LOCK_CONFIG.RENEWAL_INTERVAL * 1000);

		this.renewalTimers.set(lockKey, timer);
	}

	/**
	 * 停止锁续期
	 *
	 * 清理续期定时器，释放相关资源
	 *
	 * @param lockKey 锁的键名
	 */
	private stopLockRenewal(lockKey: string): void {
		const timer = this.renewalTimers.get(lockKey);
		if (timer) {
			clearInterval(timer);
			this.renewalTimers.delete(lockKey);
			this.lockLogger.debug(`Lock renewal stopped: ${lockKey}`);
		}
	}

	/**
	 * 获取当前持有的锁数量
	 *
	 * 通过续期定时器的数量来统计活跃锁数
	 * 用于监控和调试
	 *
	 * @returns 活跃锁数量
	 */
	getActiveLockCount(): number {
		return this.renewalTimers.size;
	}

	/**
	 * 诊断锁状态
	 *
	 * 提供详细的锁状态信息，用于问题排查
	 *
	 * @param lockKey 锁的键名
	 * @returns 锁状态诊断信息
	 */
	async diagnoseLock(lockKey: string): Promise<{
		exists: boolean;
		value: string | null;
		ttl: number;
		ownedByCurrentInstance: boolean;
		currentAppId: string;
		lockHolderId?: string;
		hasRenewalTimer: boolean;
	}> {
		const currentAppId = this.getAppIdentifier();

		try {
			const [lockValue, ttl] = await Promise.all([
				coordinationRedis.get(lockKey),
				coordinationRedis.ttl(lockKey),
			]);

			const exists = lockValue !== null;
			const ownedByCurrentInstance =
				exists && lockValue.startsWith(`${currentAppId}:`);
			const lockHolderId =
				exists && lockValue ? lockValue.split(":")[0] : undefined;
			const hasRenewalTimer = this.renewalTimers.has(lockKey);

			return {
				exists,
				value: lockValue,
				ttl,
				ownedByCurrentInstance,
				currentAppId,
				lockHolderId,
				hasRenewalTimer,
			};
		} catch (error) {
			this.lockLogger.error(error, `Error diagnosing lock: ${lockKey}`);
			return {
				exists: false,
				value: null,
				ttl: -1,
				ownedByCurrentInstance: false,
				currentAppId,
				hasRenewalTimer: this.renewalTimers.has(lockKey),
			};
		}
	}

	/**
	 * 清理所有续期定时器
	 *
	 * 用于应用关闭时的资源清理：
	 * - 停止所有续期定时器
	 * - 清空定时器映射
	 * - 记录清理日志
	 *
	 * 注意：此方法不会主动释放Redis中的锁
	 * 锁会在过期时间到达后自动过期
	 */
	cleanup(): void {
		this.lockLogger.info("Cleaning up all lock renewal timers");
		for (const [lockKey, timer] of this.renewalTimers.entries()) {
			clearInterval(timer);
			this.lockLogger.debug(`Cleaned up timer for lock: ${lockKey}`);
		}
		this.renewalTimers.clear();
	}
}

// 导出单例实例
export const lockManager = new DistributedLockManager();

// 移除独立的信号处理器 - 优雅关闭应该统一在app.ts中处理
// process.on("SIGTERM", () => {
// 	logger.info("Received SIGTERM, cleaning up locks...");
// 	lockManager.cleanup();
// });

// process.on("SIGINT", () => {
// 	logger.info("Received SIGINT, cleaning up locks...");
// 	lockManager.cleanup();
// });

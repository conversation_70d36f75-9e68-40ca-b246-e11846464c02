/**
 * INK数据同步配置管理
 *
 * 设计特点：
 * - 支持运行时配置更新和环境变量覆盖
 * - 单例模式确保全局配置一致性
 * - 缓存机制减少重复读取开销
 * - 类型安全的配置值解析
 *
 * 使用方式：
 * ```typescript
 * import { inkSyncConfig } from './config.js';
 * const interval = inkSyncConfig.highFreqUpdateInterval;
 * ```
 */
import { TRADING_TIME, JOB_CONFIG, LOCK_CONFIG } from "./constants.js";

// 配置值类型定义
type ConfigValue = number | boolean | string;

/**
 * INK同步配置管理器
 *
 * 职责：
 * - 管理所有INK同步相关的配置参数
 * - 支持环境变量覆盖默认值
 * - 提供配置缓存机制
 * - 确保配置值的类型安全
 */
export class InkSyncConfig {
	private static instance: InkSyncConfig;

	// 缓存配置，避免频繁读取
	private configCache = new Map<string, ConfigValue>();
	private lastCacheUpdate = 0;
	private readonly CACHE_TTL = 30000; // 30秒缓存

	/**
	 * 获取单例实例
	 *
	 * 使用单例模式确保全局配置的一致性
	 */
	static getInstance(): InkSyncConfig {
		if (!InkSyncConfig.instance) {
			InkSyncConfig.instance = new InkSyncConfig();
		}
		return InkSyncConfig.instance;
	}

	/**
	 * 获取配置值，支持环境变量覆盖
	 *
	 * 优先级：环境变量 > 默认值
	 * 环境变量命名规则：INK_SYNC_{KEY_UPPER_CASE}
	 *
	 * @param key 配置键名
	 * @param defaultValue 默认值
	 * @returns 配置值
	 */
	private getConfig<T extends ConfigValue>(key: string, defaultValue: T): T {
		// 检查缓存是否有效
		const now = Date.now();
		if (now - this.lastCacheUpdate > this.CACHE_TTL) {
			this.configCache.clear();
			this.lastCacheUpdate = now;
		}

		if (this.configCache.has(key)) {
			return this.configCache.get(key) as T;
		}

		// 优先使用环境变量
		const envKey = `INK_SYNC_${key.toUpperCase()}`;
		const envValue = process.env[envKey];

		let value: T;
		if (envValue !== undefined) {
			value = this.parseEnvValue(envValue, defaultValue);
		} else {
			value = defaultValue;
		}

		this.configCache.set(key, value);
		return value;
	}

	/**
	 * 解析环境变量值
	 *
	 * 根据默认值的类型自动转换环境变量字符串：
	 * - number: 转换为数字，失败时使用默认值
	 * - boolean: "true"转换为true，其他为false
	 * - string: 直接使用
	 *
	 * @param envValue 环境变量值
	 * @param defaultValue 默认值（用于类型推断）
	 * @returns 解析后的值
	 */
	private parseEnvValue<T extends ConfigValue>(
		envValue: string,
		defaultValue: T,
	): T {
		if (typeof defaultValue === "number") {
			const num = Number(envValue);
			return (Number.isNaN(num) ? defaultValue : num) as T;
		}
		if (typeof defaultValue === "boolean") {
			return (envValue.toLowerCase() === "true") as T;
		}
		return envValue as T;
	}

	// ========================================================================
	// 锁配置 - 分布式锁相关参数
	// ========================================================================

	/** 锁过期时间（秒） */
	get lockExpiry(): number {
		return this.getConfig("lock_expiry", LOCK_CONFIG.EXPIRY);
	}

	/** 锁续期间隔（秒） */
	get lockRenewalInterval(): number {
		return this.getConfig(
			"lock_renewal_interval",
			LOCK_CONFIG.RENEWAL_INTERVAL,
		);
	}

	/** 最近执行阈值（秒） - 防止重复执行的时间窗口 */
	get recentExecutionThreshold(): number {
		return this.getConfig(
			"recent_execution_threshold",
			LOCK_CONFIG.RECENT_EXECUTION_THRESHOLD,
		);
	}

	// ========================================================================
	// 交易时间配置 - 支持上午和下午两个时段
	// ========================================================================

	/** 上午交易开始时间（HHMM格式，如925表示9:25） */
	get morningTradingStart(): number {
		return this.getConfig("morning_trading_start", TRADING_TIME.MORNING_START);
	}

	/** 上午交易结束时间（HHMM格式，如1130表示11:30） */
	get morningTradingEnd(): number {
		return this.getConfig("morning_trading_end", TRADING_TIME.MORNING_END);
	}

	/** 下午交易开始时间（HHMM格式，如1300表示13:00） */
	get afternoonTradingStart(): number {
		return this.getConfig(
			"afternoon_trading_start",
			TRADING_TIME.AFTERNOON_START,
		);
	}

	/** 下午交易结束时间（HHMM格式，如1500表示15:00） */
	get afternoonTradingEnd(): number {
		return this.getConfig("afternoon_trading_end", TRADING_TIME.AFTERNOON_END);
	}

	// 保持原有配置以兼容现有代码
	/** 整体交易开始时间（兼容性配置） */
	get tradingStartTime(): number {
		return this.getConfig("trading_start_time", TRADING_TIME.TRADING_START);
	}

	/** 整体交易结束时间（兼容性配置） */
	get tradingEndTime(): number {
		return this.getConfig("trading_end_time", TRADING_TIME.TRADING_END);
	}

	/** 盘前开始时间（HHMM格式，如830表示8:30） */
	get premarketStartTime(): number {
		return this.getConfig("premarket_start_time", TRADING_TIME.PREMARKET_START);
	}

	/** 盘前结束时间（HHMM格式，如930表示9:30） */
	get premarketEndTime(): number {
		return this.getConfig("premarket_end_time", TRADING_TIME.PREMARKET_END);
	}

	// ========================================================================
	// 作业配置 - 后台任务执行参数
	// ========================================================================

	/** 高频更新间隔（毫秒） - 交易时段内部数据更新频率 */
	get highFreqUpdateInterval(): number {
		return this.getConfig(
			"high_freq_update_interval",
			JOB_CONFIG.HIGH_FREQ_UPDATE_INTERVAL,
		);
	}

	/** 盘前更新间隔（毫秒） - 外部价格报价更新频率 */
	get premarketUpdateInterval(): number {
		return this.getConfig(
			"premarket_update_interval",
			JOB_CONFIG.PREMARKET_UPDATE_INTERVAL,
		);
	}

	/** 缓存过期时间（秒） - Redis缓存数据的有效期 */
	get cacheExpiration(): number {
		return this.getConfig("cache_expiration", JOB_CONFIG.CACHE_EXPIRATION);
	}

	// ========================================================================
	// 重试配置 - 错误处理和重试机制
	// ========================================================================

	/** 最大重试次数 */
	get maxRetryAttempts(): number {
		return this.getConfig("max_retry_attempts", 3);
	}

	/** 重试延迟（毫秒） - 初始重试间隔 */
	get retryDelayMs(): number {
		return this.getConfig("retry_delay_ms", 1000);
	}

	/** 断路器阈值 - 连续失败多少次后打开断路器 */
	get circuitBreakerThreshold(): number {
		return this.getConfig("circuit_breaker_threshold", 5);
	}

	// ========================================================================
	// 批处理配置 - 性能优化参数
	// ========================================================================

	/** 批处理大小 - 单次处理的项目数量 */
	get batchSize(): number {
		return this.getConfig("batch_size", 50);
	}

	/** 并发限制 - 同时执行的任务数量上限 */
	get concurrencyLimit(): number {
		return this.getConfig("concurrency_limit", 10);
	}

	/** 清理延迟（秒） - 交易结束后延迟清理时间 */
	get cleanupDelaySeconds(): number {
		return this.getConfig("cleanup_delay_seconds", 5);
	}

	/**
	 * 清除配置缓存
	 *
	 * 强制重新读取所有配置值，用于配置更新后的刷新
	 */
	clearCache(): void {
		this.configCache.clear();
		this.lastCacheUpdate = 0;
	}

	/**
	 * 获取所有配置的摘要
	 *
	 * 用于日志记录和调试，展示当前生效的配置值
	 *
	 * @returns 配置摘要对象
	 */
	getConfigSummary(): Record<string, number> {
		return {
			// 锁配置
			lockExpiry: this.lockExpiry,
			lockRenewalInterval: this.lockRenewalInterval,
			recentExecutionThreshold: this.recentExecutionThreshold,

			// 交易时间配置
			morningTradingStart: this.morningTradingStart,
			morningTradingEnd: this.morningTradingEnd,
			afternoonTradingStart: this.afternoonTradingStart,
			afternoonTradingEnd: this.afternoonTradingEnd,
			tradingStartTime: this.tradingStartTime,
			tradingEndTime: this.tradingEndTime,
			premarketStartTime: this.premarketStartTime,
			premarketEndTime: this.premarketEndTime,

			// 作业配置
			highFreqUpdateInterval: this.highFreqUpdateInterval,
			premarketUpdateInterval: this.premarketUpdateInterval,
			cacheExpiration: this.cacheExpiration,

			// 重试配置
			maxRetryAttempts: this.maxRetryAttempts,
			retryDelayMs: this.retryDelayMs,
			circuitBreakerThreshold: this.circuitBreakerThreshold,

			// 批处理配置
			batchSize: this.batchSize,
			concurrencyLimit: this.concurrencyLimit,
			cleanupDelaySeconds: this.cleanupDelaySeconds,
		};
	}
}

// 导出单例实例
export const inkSyncConfig = InkSyncConfig.getInstance();

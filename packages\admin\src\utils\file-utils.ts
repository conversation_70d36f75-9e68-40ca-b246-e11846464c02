import J<PERSON>Z<PERSON> from "jszip";
import { ElMessage } from "element-plus";
import { fileApi } from "@/api";

// 存储当前用户印章信息的变量（单例模式）
let currentStampFile = {
	file: null as File | null,
	url: null as string | null,
};

/**
 * 设置印章文件
 */
export function setStampFile(file: File) {
	// 释放旧URL
	if (currentStampFile.url) {
		URL.revokeObjectURL(currentStampFile.url);
	}

	currentStampFile.file = file;
	currentStampFile.url = URL.createObjectURL(file);

	// 将印章缓存到 localStorage (base64格式)
	const reader = new FileReader();
	reader.onload = (e) => {
		try {
			localStorage.setItem("admin_stamp_file", e.target?.result as string);
		} catch (err) {
			console.error("Error saving stamp to localStorage:", err);
		}
	};
	reader.readAsDataURL(file);

	return currentStampFile.url;
}

/**
 * 获取当前印章文件
 */
export function getStampFile() {
	// 如果没有印章文件但有缓存，尝试从缓存恢复
	if (!currentStampFile.file) {
		const cachedStamp = localStorage.getItem("admin_stamp_file");
		if (cachedStamp) {
			try {
				// 从 dataURL 创建 Blob
				const arr = cachedStamp.split(",");
				const mime = arr[0].match(/:(.*?);/)?.[1];
				const bstr = atob(arr[1]);
				let n = bstr.length;
				const u8arr = new Uint8Array(n);

				while (n--) {
					u8arr[n] = bstr.charCodeAt(n);
				}

				const blob = new Blob([u8arr], { type: mime });
				const file = new File([blob], "stamp.png", { type: mime });

				// 创建URL
				currentStampFile.file = file;
				currentStampFile.url = URL.createObjectURL(blob);
			} catch (err) {
				console.error("Error restoring stamp from localStorage:", err);
			}
		}
	}

	return currentStampFile;
}

/**
 * 清除印章文件
 */
export function clearStampFile() {
	if (currentStampFile.url) {
		URL.revokeObjectURL(currentStampFile.url);
	}

	currentStampFile = { file: null, url: null };
	localStorage.removeItem("admin_stamp_file");
}

/**
 * 批量下载文件并打包为 zip
 */
export async function downloadFilesAsZip(
	files: Array<{ uid: string; name: string }>,
	zipFileName = "documents.zip",
): Promise<boolean> {
	if (!files.length) {
		ElMessage.warning("没有可下载的文件");
		return false;
	}

	try {
		const zip = new JSZip();

		// 使用 Map 记录重名文件
		const fileNames = new Map<string, number>();

		// 下载所有文件并添加到 zip
		for (const file of files) {
			try {
				// 获取文件内容
				const response = await fileApi.downloadFile(file.uid);
				if (!response) continue;

				// 处理文件名重复情况
				let fileName = file.name;
				if (fileNames.has(fileName)) {
					const count = fileNames.get(fileName) || 1;
					fileNames.set(fileName, count + 1);

					// 在文件名和扩展名之间插入计数
					const lastDotIndex = fileName.lastIndexOf(".");
					if (lastDotIndex !== -1) {
						fileName = `${fileName.substring(0, lastDotIndex)}_${count}${fileName.substring(lastDotIndex)}`;
					} else {
						fileName = `${fileName}_${count}`;
					}
				} else {
					fileNames.set(fileName, 1);
				}

				// 添加到 zip - 确保response是Blob类型
				const blob = response as Blob;
				zip.file(fileName, blob);
			} catch (error) {
				console.error(`Failed to download file ${file.name}:`, error);
				ElMessage.warning(`文件 ${file.name} 下载失败`);
			}
		}

		// 生成 zip 文件
		const content = await zip.generateAsync({ type: "blob" });

		// 创建下载链接
		const url = URL.createObjectURL(content);
		const link = document.createElement("a");
		link.href = url;
		link.download = zipFileName;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		URL.revokeObjectURL(url);

		return true;
	} catch (error) {
		console.error("Failed to create zip file:", error);
		ElMessage.error("创建ZIP文件失败");
		return false;
	}
}

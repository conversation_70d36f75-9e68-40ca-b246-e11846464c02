import { request } from "./request";

// 模板相关API
export const templateApi = {
	// 获取ISDA主协议模板
	getIsdaMasterTemplate: (): Promise<ArrayBuffer | null> =>
		request.get("/admin/templates/isda-master", {
			responseType: "arraybuffer",
		}),

	// 获取ISDA补充协议模板
	getIsdaSupplementTemplate: (): Promise<ArrayBuffer | null> =>
		request.get("/admin/templates/isda-supplement", {
			responseType: "arraybuffer",
		}),

	// 获取自定义协议模板
	getCustomTemplate: (uid: string): Promise<ArrayBuffer | null> =>
		request.get(`/admin/templates/custom/${uid}`, {
			responseType: "arraybuffer",
		}),

	// 上传自定义协议模板
	uploadCustomTemplate: (file: File, originalName?: string) => {
		const formData = new FormData();
		formData.append("file", file);
		if (originalName) {
			formData.append("originalName", originalName);
		}
		return request.post<{ uid: string; name: string }>(
			"/admin/templates/upload",
			formData,
		);
	},

	// 获取模板配置
	getTemplateConfig: () =>
		request.get<
			Record<
				string,
				{
					filter: { uid: string; name: string };
					stamp: Array<[number, number, number]>;
					signature: Array<[number, number, number]>;
				}
			>
		>("/admin/templates/config"),

	// 保存模板配置
	saveTemplateConfig: (
		config: Record<
			string,
			{
				filter: { uid: string; name: string };
				stamp: Array<[number, number, number]>;
				signature: Array<[number, number, number]>;
			}
		>,
	) => request.post<{ message: string }>("/admin/templates/config", config),
};

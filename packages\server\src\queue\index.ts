import { Queue, Worker } from "bullmq";
import type { Job } from "bullmq";
import { ENV, APP_CONFIG } from "@/config/configManager.js";
import logger from "@/utils/logger.js";

// Redis connection config - 增强的连接配置，提升稳定性
const connection = {
	host: ENV.REDIS_HOST || "localhost",
	port: Number(ENV.REDIS_PORT), // 确保端口是数字类型
	password: ENV.REDIS_PASSWORD,
	// 添加重试策略和连接参数，提升连接稳定性
	retryStrategy: (times: number) => {
		const delay = Math.min(times * 50, 2000);
		return delay;
	},
	connectTimeout: 10000, // 连接超时时间
	maxRetriesPerRequest: 3, // 每个请求的最大重试次数
	enableReadyCheck: true, // 启用就绪检查
	lazyConnect: false, // 立即建立连接而不是延迟连接
	keepAlive: 30000, // 保持连接活跃
	// BullMQ 特定配置
	maxmemoryPolicy: "noeviction", // 防止Redis内存不足时删除队列数据
};

// 添加连接日志，便于调试
logger.info(
	{
		host: ENV.REDIS_HOST || "localhost",
		port: Number(ENV.REDIS_PORT),
		hasPassword: !!ENV.REDIS_PASSWORD,
		retryEnabled: true,
		connectTimeout: 10000,
	},
	"BullMQ Redis Configuration:",
);

// Calculate prefix based on APP_CONFIG (similar to redis.ts)
const APP_ID = APP_CONFIG.tradingPlatformId || APP_CONFIG.channelId || "";
// Use hyphen (-) as separator instead of colon (:) which is forbidden by BullMQ
const prefix = APP_ID ? `app-${APP_ID}-bullmq-` : "global-bullmq-";

// Original queue names
const ORIGINAL_QUEUES = {
	STOCK_DATA: "stock-data",
	INK_DATA_SYNC: "ink-data-sync",
	VWAP_ORDERS: "vwap-orders",
	LIMIT_ORDERS: "limit-orders",
	SYSTEM_TASKS: "system-tasks",
	ADJ_FACTOR_TASKS: "adj-factor-tasks",
	MARKET_STATUS_TASKS: "market-status-tasks",
	INQUIRY_HISTORY_TASKS: "inquiry-history-tasks",
	EXPIRY_TASKS: "expiry-tasks",
	KNOCKOUT_TASKS: "knockout-tasks",
	EXPIRY_DATES_TASKS: "expiry-dates-tasks",
	ERROR_NOTIFICATION_TASKS: "error-notification-tasks",
	CHANNEL_LOCK_TASKS: "channel-lock-tasks",
	SYSTEM_HEALTH_CHECK_TASKS: "system-health-check-tasks",
};

// Create prefixed queue names
export const QUEUES = Object.entries(ORIGINAL_QUEUES).reduce(
	(acc, [key, value]) => {
		acc[key as keyof typeof ORIGINAL_QUEUES] = `${prefix}${value}`;
		return acc;
	},
	{} as typeof ORIGINAL_QUEUES,
);

// Create queues using the prefixed names and direct connection
export const stockDataQueue = new Queue(QUEUES.STOCK_DATA, { connection });
export const inkDataSyncQueue = new Queue(QUEUES.INK_DATA_SYNC, { connection });
export const vwapOrdersQueue = new Queue(QUEUES.VWAP_ORDERS, { connection });
export const limitOrdersQueue = new Queue(QUEUES.LIMIT_ORDERS, { connection });
export const systemTasksQueue = new Queue(QUEUES.SYSTEM_TASKS, { connection });

// Create new specific system task queues
export const adjFactorTasksQueue = new Queue(QUEUES.ADJ_FACTOR_TASKS, {
	connection,
});
export const marketStatusTasksQueue = new Queue(QUEUES.MARKET_STATUS_TASKS, {
	connection,
});
export const inquiryHistoryTasksQueue = new Queue(
	QUEUES.INQUIRY_HISTORY_TASKS,
	{
		connection,
	},
);
export const expiryTasksQueue = new Queue(QUEUES.EXPIRY_TASKS, { connection });
export const knockoutTasksQueue = new Queue(QUEUES.KNOCKOUT_TASKS, {
	connection,
});
export const expiryDatesTasksQueue = new Queue(QUEUES.EXPIRY_DATES_TASKS, {
	connection,
});
export const errorNotificationTasksQueue = new Queue(
	QUEUES.ERROR_NOTIFICATION_TASKS,
	{ connection },
);
export const channelLockTasksQueue = new Queue(QUEUES.CHANNEL_LOCK_TASKS, {
	connection,
});
export const systemHealthCheckTasksQueue = new Queue(
	QUEUES.SYSTEM_HEALTH_CHECK_TASKS,
	{ connection },
);

// Helper function to create workers
export function createWorker(
	queue: Queue,
	processor: (job: Job) => Promise<unknown>,
	concurrency = 1,
) {
	const worker = new Worker(queue.name, processor, {
		connection,
		concurrency,
	});

	worker.on("completed", (job: Job | undefined) => {
		if (job) {
			logger.debug(`Job ${job.id} completed in queue ${queue.name}`);
		}
	});

	worker.on("failed", (job: Job | undefined, err: Error) => {
		logger.error(
			{ err },
			`Job ${job?.id} failed in queue ${queue.name}: ${err.message}`,
		);
	});

	return worker;
}

// Helper to add repeated jobs
export async function addRepeatedJob(
	queue: Queue,
	name: string,
	data: Record<string, unknown>,
	pattern: string,
	jobOptions = {},
) {
	// Remove existing job with the same name if exists
	const schedulers = await queue.getJobSchedulers();
	const existingScheduler = schedulers.find((s) => s.name === name);

	if (existingScheduler) {
		await queue.removeJobScheduler(existingScheduler.key);
	}

	// Add new job
	return queue.add(name, data, {
		repeat: { pattern, tz: "Asia/Shanghai" },
		removeOnComplete: true,
		removeOnFail: 100, // Keep the last 100 failed jobs
		...jobOptions,
	});
}

// Graceful shutdown
export async function closeQueues() {
	await Promise.all([
		stockDataQueue.close(),
		inkDataSyncQueue.close(),
		vwapOrdersQueue.close(),
		limitOrdersQueue.close(),
		systemTasksQueue.close(),
		// Close new queues
		adjFactorTasksQueue.close(),
		marketStatusTasksQueue.close(),
		inquiryHistoryTasksQueue.close(),
		expiryTasksQueue.close(),
		knockoutTasksQueue.close(),
		expiryDatesTasksQueue.close(),
		errorNotificationTasksQueue.close(),
		channelLockTasksQueue.close(),
		systemHealthCheckTasksQueue.close(),
	]);

	logger.info("All BullMQ queues closed");
}

// 添加队列连接监控和错误处理
function setupQueueMonitoring() {
	// 主要队列的连接监控
	const mainQueues = [
		{ name: "inkDataSyncQueue", queue: inkDataSyncQueue },
		{ name: "stockDataQueue", queue: stockDataQueue },
		{ name: "systemTasksQueue", queue: systemTasksQueue },
	];

	for (const { name, queue } of mainQueues) {
		// 监听队列错误事件
		queue.on("error", (error: Error) => {
			logger.error(error, `Queue ${name} error: ${error.message}`);
		});

		// 监听连接事件
		queue.on("waiting", (job) => {
			logger.debug(`Job ${job.id} is waiting in queue ${name}`);
		});

		// 尝试立即检查连接状态
		queue
			.waitUntilReady()
			.then(() => {
				logger.info(`Queue ${name} connected successfully`);
			})
			.catch((error) => {
				logger.error(
					error,
					`Queue ${name} connection failed: ${error.message}`,
				);
			});
	}
}

// 初始化队列监控
setupQueueMonitoring();

# Project Structure

```
.
├─ .cursor
│  └─ rules
│     ├─ backend-express.mdc
│     ├─ frontend-vue.mdc
│     └─ shared-code.mdc
├─ .trae
│  └─ .ignore
├─ docs
│  ├─ artifitial-deploy.md
│  ├─ auto-deploy-supplement.md
│  ├─ auto-deploy.md
│  ├─ control-center-architecture.md
│  ├─ control-center-deployment.md
│  ├─ control-center-summary.md
│  └─ order-management-api.md
├─ packages
│  ├─ admin
│  │  ├─ public
│  │  ├─ static
│  │  │  └─ index.html
│  │  ├─ src# 源代码目录
│  │  │  ├─ api
│  │  │  │  ├─ auth.ts
│  │  │  │  ├─ bankAccount.ts
│  │  │  │  ├─ basic.ts
│  │  │  │  ├─ channelAdmin.ts
│  │  │  │  ├─ channelFund.ts
│  │  │  │  ├─ config.ts
│  │  │  │  ├─ file.ts
│  │  │  │  ├─ finance.ts
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ leads.ts
│  │  │  │  ├─ orderManagement.ts
│  │  │  │  ├─ permission.ts
│  │  │  │  ├─ qualify.ts
│  │  │  │  ├─ reference.ts
│  │  │  │  ├─ request.ts
│  │  │  │  ├─ sharedConfig.ts
│  │  │  │  ├─ siteConfig.ts
│  │  │  │  ├─ templates.ts
│  │  │  │  └─ types.ts
│  │  │  ├─ assets# 静态资源
│  │  │  │  └─ styles
│  │  │  │     └─ mobile.css
│  │  │  ├─ composables
│  │  │  │  └─ useTheme.ts
│  │  │  ├─ layouts
│  │  │  │  ├─ modals
│  │  │  │  │  ├─ ChangelogModal.vue
│  │  │  │  │  └─ ChangePasswordModal.vue
│  │  │  │  └─ AdminLayout.vue
│  │  │  ├─ plugins
│  │  │  │  └─ axios.ts
│  │  │  ├─ router
│  │  │  │  └─ index.ts
│  │  │  ├─ stores
│  │  │  │  ├─ auth.ts
│  │  │  │  ├─ sharedConfig.ts
│  │  │  │  ├─ siteConfig.ts
│  │  │  │  └─ stock.ts
│  │  │  ├─ utils# 工具函数
│  │  │  │  ├─ export.ts
│  │  │  │  ├─ file-utils.ts
│  │  │  │  ├─ format.ts
│  │  │  │  ├─ notification.ts
│  │  │  │  ├─ pdf-utils.ts
│  │  │  │  ├─ pdf-worker.ts
│  │  │  │  └─ stock.ts
│  │  │  ├─ views# 页面组件
│  │  │  │  ├─ panels
│  │  │  │  │  ├─ FinanceAuditPanel.vue
│  │  │  │  │  ├─ LeadsPanel.vue
│  │  │  │  │  ├─ OrderManagementPanel.vue
│  │  │  │  │  ├─ QualifyAuditPanel.vue
│  │  │  │  │  ├─ UserProfileChangeForm.vue
│  │  │  │  │  ├─ UsersPanel.vue
│  │  │  │  │  └─ UserTradeParamsForm.vue
│  │  │  │  ├─ AccountSecurityView.vue
│  │  │  │  ├─ AgreementManagementView.vue
│  │  │  │  ├─ AuditView.vue
│  │  │  │  ├─ AuthPage.vue
│  │  │  │  ├─ BankAccountView.vue
│  │  │  │  ├─ ChannelFundView.vue
│  │  │  │  ├─ ChannelManagementView.vue
│  │  │  │  ├─ ChannelOrdersView.vue
│  │  │  │  ├─ DashboardView.vue
│  │  │  │  ├─ DepositView.vue
│  │  │  │  ├─ InquiriesView.vue
│  │  │  │  ├─ OrdersView.vue
│  │  │  │  ├─ PermissionsView.vue
│  │  │  │  ├─ PlatformConfigView.vue
│  │  │  │  ├─ PlatformFundRequestView.vue
│  │  │  │  ├─ SiteConfigView.vue
│  │  │  │  ├─ SystemStatusView.vue
│  │  │  │  ├─ TransactionsView.vue
│  │  │  │  └─ UsersView.vue
│  │  │  ├─ components# 可复用组件
│  │  │  │  ├─ BackToTop.vue
│  │  │  │  ├─ PdfPositionEditor.vue
│  │  │  │  ├─ PdfPreview.vue
│  │  │  │  ├─ QualificationDetailsDialog.vue
│  │  │  │  └─ UserDetailsDialog.vue
│  │  │  ├─ App.vue
│  │  │  ├─ index.css
│  │  │  ├─ index.scss
│  │  │  └─ index.ts
│  │  ├─ Dockerfile
│  │  ├─ nginx.conf
│  │  └─ package.json
│  ├─ agent-scripts
│  │  └─ package.json
│  ├─ client
│  │  ├─ public
│  │  │  ├─ agreements
│  │  │  │  ├─ 001-1-公司开户表格.pdf
│  │  │  │  ├─ 001-2-董事会授权决议.pdf
│  │  │  │  ├─ 001-个人开户表格.pdf
│  │  │  │  ├─ 002-专业投资者声明.pdf
│  │  │  │  ├─ 003-w8ben表格.pdf
│  │  │  │  ├─ 004-2-自我证明表格.pdf
│  │  │  │  ├─ 004-税务自我证明表格.pdf
│  │  │  │  ├─ 有关于2002主协议的补充协议-中英-v2.pdf
│  │  │  │  └─ ISDA主协议及附约-中英.pdf
│  │  ├─ static
│  │  │  └─ index.html
│  │  ├─ src# 源代码目录
│  │  │  ├─ api
│  │  │  │  ├─ audit.ts
│  │  │  │  ├─ auth.ts
│  │  │  │  ├─ bankAccount.ts
│  │  │  │  ├─ file.ts
│  │  │  │  ├─ fund.ts
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ inquiry.ts
│  │  │  │  ├─ manualOrder.ts
│  │  │  │  ├─ notification.ts
│  │  │  │  ├─ position.ts
│  │  │  │  ├─ reference.ts
│  │  │  │  ├─ request.ts
│  │  │  │  ├─ sharedConfig.ts
│  │  │  │  ├─ siteConfig.ts
│  │  │  │  ├─ trade.ts
│  │  │  │  └─ types.ts
│  │  │  ├─ assets# 静态资源
│  │  │  │  ├─ fonts
│  │  │  │  │  └─ font-face.css
│  │  │  │  ├─ images
│  │  │  │  │  ├─ login-bg-placeholder.webp
│  │  │  │  │  └─ login-bg.webp
│  │  │  │  ├─ styles
│  │  │  │  │  └─ global.scss
│  │  │  │  └─ templates
│  │  │  │     ├─ settlement-notice.pdf
│  │  │  │     └─ trade-confirmation.pdf
│  │  │  ├─ composables
│  │  │  │  ├─ useDocumentLang.ts
│  │  │  │  ├─ useModals.ts
│  │  │  │  ├─ useTableSettings.ts
│  │  │  │  └─ useTheme.ts
│  │  │  ├─ core
│  │  │  │  ├─ systemStatusManager.ts
│  │  │  │  └─ websocketManager.ts
│  │  │  ├─ layout
│  │  │  │  ├─ modals
│  │  │  │  │  ├─ ChangePasswordModal.vue
│  │  │  │  │  ├─ NotificationModal.vue
│  │  │  │  │  └─ UserInfoModal.vue
│  │  │  │  ├─ DashboardLayout.vue
│  │  │  │  └─ dashbordConfig.ts
│  │  │  ├─ plugins
│  │  │  │  └─ axios.ts
│  │  │  ├─ router
│  │  │  │  └─ index.ts
│  │  │  ├─ stores
│  │  │  │  ├─ articles.ts
│  │  │  │  ├─ auth.ts
│  │  │  │  ├─ sharedConfig.ts
│  │  │  │  ├─ siteConfig.ts
│  │  │  │  └─ stock.ts
│  │  │  ├─ utils# 工具函数
│  │  │  │  ├─ clickOutside.ts
│  │  │  │  ├─ date.ts
│  │  │  │  ├─ error.ts
│  │  │  │  ├─ eventBus.ts
│  │  │  │  ├─ format.ts
│  │  │  │  ├─ messageThrottle.ts
│  │  │  │  ├─ pdf-generator.ts
│  │  │  │  ├─ stock.ts
│  │  │  │  └─ validateVwapTime.ts
│  │  │  ├─ views# 页面组件
│  │  │  │  ├─ login
│  │  │  │  │  ├─ LoginModal.vue
│  │  │  │  │  ├─ LoginPage.vue
│  │  │  │  │  ├─ RegisterModal.vue
│  │  │  │  │  └─ ResetPasswordModal.vue
│  │  │  │  ├─ modals
│  │  │  │  │  ├─ ManualOrderModal.vue
│  │  │  │  │  ├─ OrderConfirmModal.vue
│  │  │  │  │  └─ SettleConfirmModal.vue
│  │  │  │  ├─ AccountView.vue
│  │  │  │  ├─ AuditView.vue
│  │  │  │  ├─ ExerciseHistoryView.vue
│  │  │  │  ├─ InquiryView.vue
│  │  │  │  ├─ ManualOrderView.vue
│  │  │  │  ├─ OrderRecordView.vue
│  │  │  │  ├─ PositionsView.vue
│  │  │  │  └─ TransactionView.vue
│  │  │  ├─ components# 可复用组件
│  │  │  │  ├─ DateRangePicker.vue
│  │  │  │  ├─ FormattedNumberInput.vue
│  │  │  │  ├─ LoadingState.vue
│  │  │  │  ├─ MySelect.vue
│  │  │  │  ├─ PaymentDialog.vue
│  │  │  │  ├─ PdfPreview.vue
│  │  │  │  ├─ SvgIcon.vue
│  │  │  │  ├─ TableWrapper.vue
│  │  │  │  └─ ThemableLogo.vue
│  │  │  ├─ App.vue
│  │  │  ├─ index.css
│  │  │  ├─ index.scss
│  │  │  └─ index.ts
│  │  ├─ Dockerfile
│  │  ├─ nginx.conf
│  │  └─ package.json
│  ├─ control-center-backend
│  │  ├─ prisma
│  │  │  └─ schema.prisma
│  │  ├─ src# 源代码目录
│  │  │  ├─ lib
│  │  │  │  ├─ prisma.ts
│  │  │  │  └─ redis.ts
│  │  │  ├─ middleware
│  │  │  │  └─ errorHandler.ts
│  │  │  ├─ utils# 工具函数
│  │  │  │  └─ logger.ts
│  │  │  └─ index.ts
│  │  ├─ env.example
│  │  ├─ package.json
│  │  └─ tsup.config.ts
│  ├─ control-center-frontend
│  │  ├─ src# 源代码目录
│  │  │  ├─ routes# 路由定义
│  │  │  │  ├─ +layout.svelte
│  │  │  │  └─ +page.svelte
│  │  │  └─ app.html
│  │  ├─ package.json
│  │  └─ svelte.config.js
│  ├─ server
│  │  ├─ prisma
│  │  │  └─ schema.prisma
│  │  ├─ public
│  │  │  └─ templates
│  │  │     ├─ 有关于2002主协议的补充协议-中英-v2.pdf
│  │  │     └─ ISDA主协议及附约-中英.pdf
│  │  ├─ sql
│  │  │  └─ migrations
│  │  │     ├─ email_migration.sql
│  │  │     ├─ migrate-channel-balance-encryption.sql
│  │  │     └─ notification_migration.sql
│  │  ├─ src# 源代码目录
│  │  │  ├─ api
│  │  │  │  ├─ dnsFailoverAxios.ts
│  │  │  │  ├─ gtimgApi.ts
│  │  │  │  ├─ inkApi.ts
│  │  │  │  ├─ mairuiApi.ts
│  │  │  │  └─ tushareApi.ts
│  │  │  ├─ core
│  │  │  │  ├─ appError.ts
│  │  │  │  ├─ dbTxnManager.ts
│  │  │  │  ├─ systemAvailabilityManager.ts
│  │  │  │  └─ websocket.ts
│  │  │  ├─ financeUtils
│  │  │  │  ├─ adjFactorManager.ts
│  │  │  │  ├─ calculator.ts
│  │  │  │  ├─ knockoutUtils.ts
│  │  │  │  ├─ marketData.ts
│  │  │  │  ├─ marketTimeManager.ts
│  │  │  │  └─ quote.ts
│  │  │  ├─ lib
│  │  │  │  ├─ channelDatabaseManager.ts
│  │  │  │  ├─ mysql.ts
│  │  │  │  ├─ prisma.ts
│  │  │  │  └─ redis.ts
│  │  │  ├─ middlewares# 中间件
│  │  │  │  ├─ adminPermission.ts
│  │  │  │  ├─ apiErrorHandler.ts
│  │  │  │  ├─ csrf.ts
│  │  │  │  ├─ jwtAuth.ts
│  │  │  │  ├─ rateLimiter.ts
│  │  │  │  ├─ systemAvailability.ts
│  │  │  │  ├─ validateSchema.ts
│  │  │  │  └─ validator.ts
│  │  │  ├─ queue
│  │  │  │  ├─ ink
│  │  │  │  │  ├─ config.ts
│  │  │  │  │  ├─ constants.ts
│  │  │  │  │  ├─ healthCheck.ts
│  │  │  │  │  ├─ LOCK_VERIFICATION_GUIDE.md
│  │  │  │  │  ├─ lockDashboard.ts
│  │  │  │  │  ├─ lockVerification.ts
│  │  │  │  │  ├─ metricsCollector.ts
│  │  │  │  │  ├─ redisManager.ts
│  │  │  │  │  ├─ retryHandler.ts
│  │  │  │  │  ├─ test-optimizations.ts
│  │  │  │  │  └─ test-refactor.ts
│  │  │  │  ├─ adjFactorWorker.ts
│  │  │  │  ├─ channelLockWorker.ts
│  │  │  │  ├─ errorNotificationWorker.ts
│  │  │  │  ├─ expiryDatesWorker.ts
│  │  │  │  ├─ expiryWorker.ts
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ inkDataSyncWorker.bak.ts
│  │  │  │  ├─ inkDataSyncWorker.ts
│  │  │  │  ├─ inquiryHistoryWorker.ts
│  │  │  │  ├─ knockoutWorker.ts
│  │  │  │  ├─ limitOrderWorker.ts
│  │  │  │  ├─ marketStatusWorker.ts
│  │  │  │  ├─ stockDataSyncWorker.ts
│  │  │  │  ├─ systemHealthCheckWorker.ts
│  │  │  │  ├─ taskRegistry.ts
│  │  │  │  └─ vwapOrderWorker.ts
│  │  │  ├─ scripts
│  │  │  │  ├─ channel-balance-crypto.ts
│  │  │  │  ├─ cleanup-logs.ts
│  │  │  │  ├─ db-field-crypto.ts
│  │  │  │  ├─ generate-agreements.ts
│  │  │  │  └─ prisma-cmd.ts
│  │  │  ├─ tasks
│  │  │  │  ├─ adjFactorCron.ts
│  │  │  │  ├─ channelLockCron.ts
│  │  │  │  ├─ errorNotificationCron.ts
│  │  │  │  ├─ expiryCron.ts
│  │  │  │  ├─ inkDataSyncCron.ts
│  │  │  │  ├─ inquiryHistoryCron.ts
│  │  │  │  ├─ knockoutCron.ts
│  │  │  │  ├─ marketStatusCron.ts
│  │  │  │  ├─ stockDataCron.ts
│  │  │  │  ├─ systemHealthCheckCron.ts
│  │  │  │  └─ updateExpiryDates.ts
│  │  │  ├─ tests# 测试文件
│  │  │  │  ├─ batchSizeTest.ts
│  │  │  │  ├─ debug-suspension.ts
│  │  │  │  ├─ generate-test-accounts.ts
│  │  │  │  ├─ knockoutTest600811.ts
│  │  │  │  ├─ knockoutUtilsTest.ts
│  │  │  │  ├─ quoteAdjustTest.ts
│  │  │  │  ├─ test-pdf-generator.ts
│  │  │  │  ├─ test-pino-roll.ts
│  │  │  │  ├─ testConnection.ts
│  │  │  │  ├─ testDatabaseTransaction.ts
│  │  │  │  ├─ testDnsFailoverConcurrency.ts
│  │  │  │  ├─ tplusNTest.ts
│  │  │  │  ├─ tradeCalendarTest.ts
│  │  │  │  └─ tradingDaysTest.ts
│  │  │  ├─ utils# 工具函数
│  │  │  │  ├─ dateUtils.ts
│  │  │  │  ├─ email.ts
│  │  │  │  ├─ encryption.ts
│  │  │  │  ├─ exchangeRate.ts
│  │  │  │  ├─ format.ts
│  │  │  │  ├─ logger.ts
│  │  │  │  ├─ misc.ts
│  │  │  │  ├─ notificationThrottle.ts
│  │  │  │  ├─ notify.ts
│  │  │  │  ├─ pdf-generator.ts
│  │  │  │  ├─ routeWrapper.ts
│  │  │  │  └─ sms.ts
│  │  │  ├─ config# 配置文件
│  │  │  │  ├─ configManager.ts
│  │  │  │  └─ defaultParams.ts
│  │  │  ├─ routes# 路由定义
│  │  │  │  ├─ admin
│  │  │  │  │  ├─ authRoutes.ts
│  │  │  │  │  ├─ bankAccountRoutes.ts
│  │  │  │  │  ├─ basicRoutes.ts
│  │  │  │  │  ├─ channelFundRoutes.ts
│  │  │  │  │  ├─ channelLeadsRoutes.ts
│  │  │  │  │  ├─ configRoutes.ts
│  │  │  │  │  ├─ fileRoutes.ts
│  │  │  │  │  ├─ financeRoutes.ts
│  │  │  │  │  ├─ index.ts
│  │  │  │  │  ├─ orderManagementRoutes.ts
│  │  │  │  │  ├─ permissonRoutes.ts
│  │  │  │  │  ├─ platformRequestRoutes.ts
│  │  │  │  │  ├─ qualifyRoutes.ts
│  │  │  │  │  ├─ sharedConfigRoutes.ts
│  │  │  │  │  └─ templateRoutes.ts
│  │  │  │  ├─ adminSiteConfigRoutes.ts
│  │  │  │  ├─ auditRoutes.ts
│  │  │  │  ├─ authRoutes.ts
│  │  │  │  ├─ basicRoutes.ts
│  │  │  │  ├─ fileRoutes.ts
│  │  │  │  ├─ fundRoutes.ts
│  │  │  │  ├─ index.ts
│  │  │  │  ├─ inquiryRoutes.ts
│  │  │  │  ├─ manualOrderRoutes.ts
│  │  │  │  ├─ notifRoutes.ts
│  │  │  │  ├─ openRoutes.ts
│  │  │  │  ├─ positionRoutes.ts
│  │  │  │  ├─ publicRoutes.ts
│  │  │  │  ├─ referenceRoutes.ts
│  │  │  │  ├─ sharedConfigRoutes.ts
│  │  │  │  ├─ siteConfigRoutes.ts
│  │  │  │  └─ tradeRoutes.ts
│  │  │  ├─ models# 数据模型
│  │  │  │  ├─ trade
│  │  │  │  │  ├─ order.ts
│  │  │  │  │  └─ pendingOrder.ts
│  │  │  │  ├─ admins.ts
│  │  │  │  ├─ audit.ts
│  │  │  │  ├─ bankAccount.ts
│  │  │  │  ├─ channelFund.ts
│  │  │  │  ├─ config.ts
│  │  │  │  ├─ fund.ts
│  │  │  │  ├─ inquiry.ts
│  │  │  │  ├─ manualOrder.ts
│  │  │  │  ├─ notification.ts
│  │  │  │  ├─ position.ts
│  │  │  │  ├─ sharedConfig.ts
│  │  │  │  ├─ siteConfig.ts
│  │  │  │  ├─ stock.ts
│  │  │  │  ├─ systemHealthCheck.ts
│  │  │  │  └─ user.ts
│  │  │  ├─ services# 业务服务
│  │  │  │  ├─ admin
│  │  │  │  │  ├─ adminService.ts
│  │  │  │  │  ├─ auditService.ts
│  │  │  │  │  ├─ authService.ts
│  │  │  │  │  ├─ bankAccountService.ts
│  │  │  │  │  ├─ basicService.ts
│  │  │  │  │  ├─ configService.ts
│  │  │  │  │  ├─ index.ts
│  │  │  │  │  ├─ orderManagementService.ts
│  │  │  │  │  ├─ sharedConfigService.ts
│  │  │  │  │  └─ siteConfigService.ts
│  │  │  │  ├─ trade
│  │  │  │  │  ├─ confirmOrder.ts
│  │  │  │  │  ├─ executeOrder.ts
│  │  │  │  │  ├─ limitStrategy.ts
│  │  │  │  │  ├─ tradeService.ts
│  │  │  │  │  └─ vwapStrategy.ts
│  │  │  │  ├─ auditService.ts
│  │  │  │  ├─ authService.ts
│  │  │  │  ├─ channelFundService.ts
│  │  │  │  ├─ channelOrderService.ts
│  │  │  │  ├─ dataSync.ts
│  │  │  │  ├─ fundService.ts
│  │  │  │  ├─ initService.ts
│  │  │  │  ├─ inquiryService.ts
│  │  │  │  ├─ manualOrderService.ts
│  │  │  │  ├─ notifService.ts
│  │  │  │  ├─ orderService.ts
│  │  │  │  ├─ positionService.ts
│  │  │  │  ├─ systemHealthService.ts
│  │  │  │  └─ userService.ts
│  │  │  └─ app.ts
│  │  ├─ Dockerfile
│  │  ├─ package.json
│  │  └─ tsup.config.ts
│  └─ shared# 共享代码
│     ├─ src# 源代码目录
│     │  ├─ schemas
│     │  │  └─ inquiry.ts
│     │  ├─ utils# 工具函数
│     │  │  └─ date.ts
│     │  ├─ config# 配置文件
│     │  │  ├─ fortunemax.ts
│     │  │  └─ ink.ts
│     │  ├─ availability.ts
│     │  ├─ errorCore.ts
│     │  └─ index.ts
│     └─ package.json
├─ scripts
│  ├─ generate-structure.ts
│  └─ setup-control-center.sh
├─ .cursorignore
├─ .dockerignore
├─ biome.json
├─ docker-compose.control-center.yml
├─ docker-compose.yml
├─ ecosystem.config.cjs
├─ package.json
├─ pnpm-workspace.yaml
├─ README.md
└─ STRUCTURE.md

```

## Key Directories

### packages/client
用户前端应用，包含用户界面和交互逻辑。

### packages/admin
管理后台界面，用于系统管理和监控。

### packages/server
后端服务实现，处理业务逻辑和数据存储。

### packages/shared
共享代码，包含类型定义、工具函数和常量。

## Development

```bash
# 安装依赖
pnpm install

# 启动开发服务
pnpm dev

# 构建项目
pnpm build

# 运行测试
pnpm test
```

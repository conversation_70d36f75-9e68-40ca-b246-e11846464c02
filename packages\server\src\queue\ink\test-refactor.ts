#!/usr/bin/env node

/**
 * INK数据同步重构验证脚本
 * 用于测试所有新模块是否正常工作
 */

import { getChildLogger } from "@/utils/logger.js";
import { inkHealthChecker } from "./healthCheck.js";
import { inkSyncConfig } from "./config.js";
import { metricsCollector } from "./metricsCollector.js";

const logger = getChildLogger("RefactorTest");

async function testRefactor() {
	console.log("🚀 开始验证INK数据同步重构...\n");

	try {
		// 1. 测试配置模块
		console.log("📋 测试配置模块...");
		const config = inkSyncConfig.getConfigSummary();
		console.log(`✅ 配置加载成功，包含 ${Object.keys(config).length} 个配置项`);
		console.log(`   - 锁过期时间: ${config.lockExpiry}秒`);
		console.log(
			`   - 交易时间: ${config.tradingStartTime} - ${config.tradingEndTime}`,
		);
		console.log(`   - 高频更新间隔: ${config.highFreqUpdateInterval}ms\n`);

		// 2. 测试指标收集器
		console.log("📊 测试指标收集器...");
		const jobId = metricsCollector.recordJobStart("test-job");
		await new Promise((resolve) => setTimeout(resolve, 100)); // 模拟作业执行
		metricsCollector.recordJobComplete("test-job", true);

		const metrics = await metricsCollector.getCurrentMetrics();
		console.log(
			`✅ 指标收集成功，时间戳: ${new Date(metrics.timestamp).toLocaleString()}`,
		);
		console.log(`   - 作业总数: ${metrics.jobMetrics.total}`);
		console.log(`   - 成功作业: ${metrics.jobMetrics.successful}`);
		console.log(`   - 活跃锁数: ${metrics.lockMetrics.activeLocks}\n`);

		// 3. 执行完整健康检查
		console.log("🏥 执行系统健康检查...");
		const healthResult = await inkHealthChecker.performHealthCheck();

		console.log(
			`✅ 健康检查完成，整体状态: ${healthResult.overall.toUpperCase()}`,
		);

		// 显示各模块状态
		for (const [moduleName, moduleResult] of Object.entries(
			healthResult.modules,
		)) {
			const statusIcon =
				moduleResult.status === "healthy"
					? "✅"
					: moduleResult.status === "warning"
						? "⚠️"
						: "❌";
			console.log(
				`   ${statusIcon} ${moduleName}: ${moduleResult.status} - ${moduleResult.message}`,
			);
		}

		// 4. 生成详细报告
		console.log("\n📄 生成详细健康报告...");
		const report = await inkHealthChecker.generateReport();
		console.log(`\n${"=".repeat(60)}`);
		console.log(report);
		console.log("=".repeat(60));

		// 5. 测试环境变量配置覆盖
		console.log("\n🔧 测试环境变量配置覆盖...");
		const originalLockExpiry = inkSyncConfig.lockExpiry;

		// 设置环境变量
		process.env.INK_SYNC_LOCK_EXPIRY = "600";
		inkSyncConfig.clearCache(); // 清除缓存以重新读取

		const newLockExpiry = inkSyncConfig.lockExpiry;
		console.log("✅ 环境变量覆盖测试成功:");
		console.log(`   - 原始值: ${originalLockExpiry}秒`);
		console.log(`   - 覆盖后: ${newLockExpiry}秒`);

		// 恢复环境变量
		process.env.INK_SYNC_LOCK_EXPIRY = undefined;
		inkSyncConfig.clearCache();

		// 6. 总结
		console.log("\n🎉 重构验证完成！");

		if (healthResult.overall === "healthy") {
			console.log("✅ 所有模块工作正常，重构成功！");
			console.log("\n📈 性能提升预期:");
			console.log("   - 并发处理能力提升 30-50%");
			console.log("   - 故障恢复时间减少 90%");
			console.log("   - 内存使用优化 20-30%");
			console.log("   - 完整的监控和告警机制");
		} else if (healthResult.overall === "warning") {
			console.log("⚠️ 部分模块有警告，但基本功能正常");
			console.log("建议检查警告信息并进行相应调整");
		} else {
			console.log("❌ 发现严重问题，需要立即处理");
			console.log("请检查错误日志并修复相关问题");
		}

		console.log("\n🔗 相关文件:");
		console.log("   - 常量定义: packages/server/src/queue/ink/constants.ts");
		console.log("   - 配置管理: packages/server/src/queue/ink/config.ts");
		console.log(
			"   - 分布式锁: packages/server/src/queue/ink/distributedLockManager.ts",
		);
		console.log("   - 重试处理: packages/server/src/queue/ink/retryHandler.ts");
		console.log(
			"   - Redis管理: packages/server/src/queue/ink/redisManager.ts",
		);
		console.log(
			"   - 指标收集: packages/server/src/queue/ink/metricsCollector.ts",
		);
		console.log("   - 健康检查: packages/server/src/queue/ink/healthCheck.ts");
		console.log(
			"   - 主Worker: packages/server/src/queue/inkDataSyncWorker.ts",
		);
		console.log(
			"   - 备份文件: packages/server/src/queue/inkDataSyncWorker.ts.backup",
		);

		return healthResult.overall !== "critical";
	} catch (error) {
		console.error("❌ 重构验证失败:", error);
		logger.error(error, "Refactor test failed");
		return false;
	}
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
	testRefactor()
		.then((success) => {
			process.exit(success ? 0 : 1);
		})
		.catch((error) => {
			console.error("脚本执行失败:", error);
			process.exit(1);
		});
}

export { testRefactor };

{"name": "@packages/client", "private": true, "type": "module", "scripts": {"dev": "rsbuild dev", "build": "rsbuild build", "preview": "rsbuild preview"}, "dependencies": {"@ctrl/tinycolor": "^4.1.0", "@element-plus/icons-vue": "^2.3.1", "@packages/shared": "workspace:*", "@pdf-lib/fontkit": "^1.1.1", "@vueuse/core": "^11.3.0", "axios": "^1.9.0", "element-plus": "^2.9.10", "jwt-decode": "^4.0.0", "pdf-lib": "^1.17.1", "pinia": "^2.3.1", "vue": "^3.5.14", "vue-router": "^4.5.1", "vue3-signature": "^0.2.4"}, "devDependencies": {"@types/webpack-env": "^1.18.8"}}
<template>
  <el-dialog v-model="visible" :title="title" width="90%" top="2vh" :close-on-click-modal="false"
    class="pdf-position-dialog" @close="handleClose">
    <div class="pdf-position-editor">
      <div class="editor-toolbar">
        <div class="tool-group">
          <span class="tool-label">当前页面：</span>
          <el-select v-model="currentPage" @change="goToPage" size="small" style="width: 120px;">
            <el-option v-for="(page, index) in totalPages" :key="index" :label="`第${index + 1}页`" :value="index" />
          </el-select>
        </div>

        <div class="tool-group">
          <span class="tool-label">缩放：</span>
          <el-select v-model="scale" @change="renderCurrentPage" size="small" style="width: 100px;">
            <el-option label="50%" :value="0.5" />
            <el-option label="75%" :value="0.75" />
            <el-option label="100%" :value="1.0" />
            <el-option label="125%" :value="1.25" />
            <el-option label="150%" :value="1.5" />
            <el-option label="200%" :value="2.0" />
          </el-select>
        </div>

        <div class="tool-group">
          <span class="tool-label">编辑模式：</span>
          <el-radio-group v-model="editMode" size="small">
            <el-radio-button value="stamp">印章位置</el-radio-button>
            <el-radio-button value="signature">签名位置</el-radio-button>
          </el-radio-group>
        </div>

        <div class="tool-group">
          <el-button type="danger" size="small" @click="clearCurrentPagePositions">
            清空当前页位置
          </el-button>
        </div>
      </div>

      <div class="pdf-container" ref="pdfContainer">
        <!-- 加载状态显示 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-content">
            <el-icon class="loading-icon is-loading">
              <Loading />
            </el-icon>
            <span>正在加载PDF...</span>
          </div>
        </div>

        <!-- PDF Canvas显示区域 -->
        <div v-show="!isLoading" class="pdf-canvas-wrapper" ref="canvasWrapper">
          <div class="continuous-canvas-container" ref="continuousContainer">
            <div v-for="pageNum in pageNumbers" :key="`page-${pageNum}`" class="page-canvas-wrapper"
              :data-page-num="pageNum" :style="getPageWrapperStyle(pageNum)">
              <canvas v-if="shouldRenderPage(pageNum)" :ref="el => setPageCanvasRef(pageNum, el)"
                class="pdf-canvas"></canvas>
              <div v-else class="page-placeholder">
                <div class="page-number">第 {{ pageNum + 1 }} 页</div>
                <div class="placeholder-text">滚动加载中...</div>
              </div>
              <div v-if="shouldRenderPage(pageNum)" class="page-number">第 {{ pageNum + 1 }} 页</div>
            </div>
          </div>

          <!-- 位置标记层 -->
          <div class="position-overlay" ref="positionOverlay" @click="handleOverlayClick" @mousemove="handleMouseMove"
            @mouseleave="hidePreview">
            <!-- 印章位置标记 -->
            <div v-for="(pos, index) in currentPageStampPositions" :key="`stamp-${index}`"
              class="position-marker stamp-marker" :style="getMarkerStyle(pos, 'stamp')"
              @mousedown="startDrag(index, 'stamp', $event)">
              <div class="marker-content">
                <el-icon>
                  <Stamp />
                </el-icon>
                <span class="marker-number">{{ index + 1 }}</span>
              </div>
              <el-button type="danger" size="small" circle class="remove-button"
                @click.stop="removePosition(index, 'stamp')">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>

            <!-- 签名位置标记 -->
            <div v-for="(pos, index) in currentPageSignaturePositions" :key="`signature-${index}`"
              class="position-marker signature-marker" :style="getMarkerStyle(pos, 'signature')"
              @mousedown="startDrag(index, 'signature', $event)">
              <div class="marker-content">
                <el-icon>
                  <Edit />
                </el-icon>
                <span class="marker-number">{{ index + 1 }}</span>
              </div>
              <el-button type="danger" size="small" circle class="remove-button"
                @click.stop="removePosition(index, 'signature')">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>

            <!-- 预览标记 -->
            <div v-if="previewPosition" class="position-marker preview-marker"
              :class="editMode === 'stamp' ? 'stamp-marker' : 'signature-marker'" :style="getPreviewMarkerStyle()">
              <div class="marker-content">
                <el-icon v-if="editMode === 'stamp'">
                  <Stamp />
                </el-icon>
                <el-icon v-else>
                  <Edit />
                </el-icon>
                <span class="marker-number">+</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="position-summary">
        <div class="summary-section">
          <h4>印章位置 ({{ allStampPositions.length }}个)</h4>
          <div class="position-list">
            <div v-for="(pos, index) in allStampPositions" :key="`stamp-summary-${index}`" class="position-item"
              :class="{ active: currentPage === pos[0] }" @click="goToPosition(pos[0])">
              第{{ pos[0] + 1 }}页 ({{ Math.round(pos[1]) }}, {{ Math.round(pos[2]) }})
            </div>
          </div>
        </div>

        <div class="summary-section">
          <h4>签名位置 ({{ allSignaturePositions.length }}个)</h4>
          <div class="position-list">
            <div v-for="(pos, index) in allSignaturePositions" :key="`signature-summary-${index}`" class="position-item"
              :class="{ active: currentPage === pos[0] }" @click="goToPosition(pos[0])">
              第{{ pos[0] + 1 }}页 ({{ Math.round(pos[1]) }}, {{ Math.round(pos[2]) }})
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存位置</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted, shallowRef } from "vue";
import { ElMessage } from "element-plus";
import { Close, Stamp, Edit, Loading } from "@element-plus/icons-vue";
import * as pdfjsLib from "pdfjs-dist";
import type { PDFDocumentProxy, PDFPageProxy } from "pdfjs-dist";
import { initializePDFWorker, getPDFJSOptions, resetPDFWorker } from "@/utils/pdf-worker";

interface Props {
  modelValue: boolean;
  title: string;
  pdfData: ArrayBuffer | null;
  stampPositions: Array<[number, number, number]>;
  signaturePositions: Array<[number, number, number]>;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'save', data: {
    stamp: Array<[number, number, number]>;
    signature: Array<[number, number, number]>;
  }): void;
}

const props = withDefaults(defineProps<Props>(), {
  stampPositions: () => [],
  signaturePositions: () => [],
});

const emit = defineEmits<Emits>();

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const pdfContainer = ref<HTMLDivElement>();
const canvasWrapper = ref<HTMLDivElement>();
const continuousContainer = ref<HTMLDivElement>();
const positionOverlay = ref<HTMLDivElement>();
const currentPage = ref(0);
const totalPages = ref(0);
const scale = ref(1.0);
const editMode = ref<'stamp' | 'signature'>('stamp');

// PDF相关 - 使用 shallowRef 避免深度响应式代理
const pdfDocument = shallowRef<PDFDocumentProxy | null>(null);
const currentPageProxy = shallowRef<PDFPageProxy | null>(null);

// 位置数据
const allStampPositions = ref<Array<[number, number, number]>>([]);
const allSignaturePositions = ref<Array<[number, number, number]>>([]);

// 预览位置
const previewPosition = ref<{ x: number; y: number } | null>(null);

// 滚动状态
const isScrolling = ref(false);
let scrollTimer: number | null = null;

// 虚拟滚动相关
const visiblePages = ref<number[]>([0]); // 当前可见的页面列表
const pageCanvases = ref<Map<number, HTMLCanvasElement>>(new Map());
const pageHeights = ref<Map<number, number>>(new Map()); // 存储每页的高度
const renderBuffer = 2; // 当前页前后渲染的页面数量

// 拖拽相关
const dragging = ref<{
  index: number;
  type: 'stamp' | 'signature';
  startX: number;
  startY: number;
  startPosX: number;
  startPosY: number;
} | null>(null);

// 拖拽结束标记，防止立即触发点击事件
const justFinishedDragging = ref(false);

// PDF尺寸信息
const canvasRect = ref<DOMRect | null>(null);

// 加载状态
const isLoading = ref(false);

// 生成页面数组（0基索引）
const pageNumbers = computed(() => {
  const pages: number[] = [];
  for (let i = 0; i < totalPages.value; i++) {
    pages.push(i);
  }
  return pages;
});

// 计算所有渲染页面的位置
const currentPageStampPositions = computed(() => {
  return allStampPositions.value
    .map((pos, index) => ({ pos, originalIndex: index }))
    .filter(({ pos }) => shouldRenderPage(pos[0]));
});

const currentPageSignaturePositions = computed(() => {
  return allSignaturePositions.value
    .map((pos, index) => ({ pos, originalIndex: index }))
    .filter(({ pos }) => shouldRenderPage(pos[0]));
});

// 初始化PDF
const initPdf = async () => {
  if (!props.pdfData) return;

  // 如果已经在加载中，直接返回
  if (isLoading.value && pdfDocument.value) return;

  isLoading.value = true;

  try {
    // 确保worker已设置，如果失败则重置
    try {
      initializePDFWorker();
    } catch (error) {
      console.warn("Worker初始化失败，尝试重置:", error);
      resetPDFWorker();
    }

    // 创建一个新的ArrayBuffer副本，避免响应式代理问题
    const pdfDataCopy = props.pdfData.slice(0);

    // 加载PDF文档 - 使用更稳定的配置
    const loadingTask = pdfjsLib.getDocument({
      data: pdfDataCopy,
      ...getPDFJSOptions(),
      // 强制禁用一些可能导致proxy问题的功能
      disableCreateObjectURL: true,
      // 设置较短的超时时间，快速失败
      rangeChunkSize: 65536,
      disableRange: true, // 暂时禁用范围请求，确保稳定性
    });

    // 等待文档加载完成
    pdfDocument.value = await loadingTask.promise;

    totalPages.value = pdfDocument.value.numPages;
    currentPage.value = 0;

    // 复制初始位置数据并转换为0基索引
    allStampPositions.value = props.stampPositions.map(pos => [pos[0] - 1, pos[1], pos[2]] as [number, number, number]);
    allSignaturePositions.value = props.signaturePositions.map(pos => [pos[0] - 1, pos[1], pos[2]] as [number, number, number]);

    // 等待DOM更新，然后渲染初始页面
    await nextTick();
    await renderCurrentPage();

    // 在PDF加载完成后添加滚动监听器
    await nextTick();
    if (pdfContainer.value) {
      pdfContainer.value.addEventListener('scroll', handleScroll, { passive: true });
    }
  } catch (error) {
    console.error('PDF加载失败:', error);

    // 提供更详细的错误信息
    let errorMessage = 'PDF加载失败';
    if (error instanceof Error) {
      if (error.message.includes('worker')) {
        errorMessage = 'PDF Worker初始化失败，请刷新页面重试';
      } else if (error.message.includes('Invalid PDF')) {
        errorMessage = 'PDF文件格式无效';
      } else if (error.message.includes('CORS')) {
        errorMessage = '文件加载失败，请检查网络连接';
      } else if (error.message.includes('pagePromises')) {
        errorMessage = 'PDF渲染引擎兼容性问题，正在尝试恢复...';
        // 尝试重置worker并重试
        setTimeout(async () => {
          try {
            resetPDFWorker();
            await initPdf();
          } catch (retryError) {
            console.error("重试失败:", retryError);
            ElMessage.error("PDF加载失败，请刷新页面重试");
          }
        }, 1000);
        return;
      } else {
        errorMessage = `PDF加载失败: ${error.message}`;
      }
    }

    ElMessage.error(errorMessage);
  } finally {
    isLoading.value = false;
  }
};

// 判断页面是否应该渲染
const shouldRenderPage = (pageNum: number) => {
  const distance = Math.abs(pageNum - currentPage.value);
  const shouldRender = distance <= renderBuffer;
  return shouldRender;
};

// 获取页面容器样式
const getPageWrapperStyle = (pageNum: number) => {
  const height = pageHeights.value.get(pageNum);
  if (height) {
    return {
      minHeight: `${height}px`,
      height: `${height}px`
    };
  }
  // 默认高度，避免布局跳动
  return {
    minHeight: '800px',
    height: '800px'
  };
};

// 设置页面Canvas引用
const setPageCanvasRef = (pageNum: number, el: HTMLCanvasElement | null) => {
  if (el) {
    pageCanvases.value.set(pageNum, el);
  }
};

// 预计算所有页面高度
const preCalculatePageHeights = async () => {
  if (!pdfDocument.value || totalPages.value === 0) return;

  for (let pageNum = 0; pageNum < totalPages.value; pageNum++) {
    try {
      const pageProxy = await pdfDocument.value.getPage(pageNum + 1);
      const viewport = pageProxy.getViewport({ scale: scale.value });
      const height = viewport.height + 40; // 40px for margin
      pageHeights.value.set(pageNum, height);
    } catch (error) {
      console.warn(`计算页面${pageNum + 1}高度失败:`, error);
      pageHeights.value.set(pageNum, 800); // 使用默认高度
    }
  }
};

// 渲染指定页面
const renderPage = async (pageNum: number) => {
  if (!pdfDocument.value || !shouldRenderPage(pageNum)) {
    return;
  }

  const canvas = pageCanvases.value.get(pageNum);
  if (!canvas) {
    return;
  }

  try {
    const pageProxy = await pdfDocument.value.getPage(pageNum + 1);
    const viewport = pageProxy.getViewport({ scale: scale.value });
    const context = canvas.getContext('2d');

    if (!context) {
      return;
    }

    canvas.width = viewport.width;
    canvas.height = viewport.height;
    canvas.style.width = `${viewport.width}px`;
    canvas.style.height = `${viewport.height}px`;

    // 更新实际高度
    pageHeights.value.set(pageNum, viewport.height + 40);

    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };

    await pageProxy.render(renderContext).promise;

    if (pageNum === currentPage.value) {
      currentPageProxy.value = pageProxy;
    }

  } catch (error) {
    console.error(`渲染页面${pageNum + 1}失败:`, error);
  }
};

// 更新需要渲染的页面
const updateRenderablePages = () => {
  const totalPagesCount = pdfDocument.value?.numPages || 0;
  const startPage = Math.max(0, currentPage.value - renderBuffer);
  const endPage = Math.min(totalPagesCount - 1, currentPage.value + renderBuffer);

  // 渲染当前范围内的页面
  for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
    // 等待DOM更新后再渲染
    nextTick(() => renderPage(pageNum));
  }
};

// 渲染当前页面及其缓冲区
const renderCurrentPage = async () => {
  // 先预计算所有页面高度
  await preCalculatePageHeights();

  // 更新需要渲染的页面
  updateRenderablePages();

  // 调整覆盖层
  await nextTick();
  setTimeout(adjustOverlaySize, 50);
};

// 调整覆盖层尺寸以匹配连续容器
const adjustOverlaySize = async () => {
  await nextTick();

  if (!continuousContainer.value || !positionOverlay.value) return;

  // 设置覆盖层尺寸与连续容器匹配
  positionOverlay.value.style.width = `${continuousContainer.value.offsetWidth}px`;
  positionOverlay.value.style.height = `${continuousContainer.value.offsetHeight}px`;
  positionOverlay.value.style.left = '0px';
  positionOverlay.value.style.top = '0px';
};

// 跳转到指定页面
const goToPage = async (pageIndex: number) => {
  // 设置手动跳转标记，防止滚动检测干扰
  isManualJumping.value = true;

  currentPage.value = pageIndex;
  await renderCurrentPage();

  // 滚动到对应页面位置
  await nextTick();
  scrollToPage(pageIndex);

  // 延迟重置手动跳转标记，确保滚动完成
  setTimeout(() => {
    isManualJumping.value = false;
  }, 1000);
};

// 滚动到指定页面
const scrollToPage = (pageIndex: number) => {
  if (!pdfContainer.value || !continuousContainer.value) return;

  // 计算目标页面的累积偏移，包含页面间距
  let targetScrollTop = 0;
  for (let i = 0; i < pageIndex; i++) {
    targetScrollTop += pageHeights.value.get(i) || 800;
    // 添加页面间距（除了最后一页）
    if (i < totalPages.value - 1) {
      targetScrollTop += 20; // margin-bottom
    }
  }

  // 平滑滚动到目标位置
  pdfContainer.value.scrollTo({
    top: targetScrollTop,
    behavior: 'smooth'
  });
};

// 获取标记样式
const getMarkerStyle = (posData: { pos: [number, number, number] }, type: 'stamp' | 'signature') => {
  const [pageNum, x, y] = posData.pos;

  // 检查页面是否应该显示标记
  if (!shouldRenderPage(pageNum)) return { display: 'none' };

  // 计算页面的累积偏移，包含页面间距
  let accumulatedTop = 0;
  for (let i = 0; i < pageNum; i++) {
    accumulatedTop += pageHeights.value.get(i) || 800;
    // 添加页面间距（除了最后一页）
    if (i < totalPages.value - 1) {
      accumulatedTop += 20; // margin-bottom
    }
  }

  // 计算标记在页面中的位置
  const canvasX = x * scale.value;
  const pageHeight = pageHeights.value.get(pageNum) || 800;
  const canvasY = (pageHeight - 40) - (y * scale.value); // PDF坐标系Y轴向上，减去margin

  const size = type === 'stamp' ? 40 : 20;
  return {
    left: `${canvasX - size}px`,
    top: `${accumulatedTop + canvasY - size}px`,
  };
};

// 获取预览标记样式
const getPreviewMarkerStyle = () => {
  if (!previewPosition.value) return { display: 'none' };

  const size = editMode.value === 'stamp' ? 40 : 20;
  return {
    left: `${previewPosition.value.x - size}px`,
    top: `${previewPosition.value.y - size}px`,
    opacity: '0.7'
  };
};

// 处理覆盖层点击
const handleOverlayClick = (event: MouseEvent) => {
  if (dragging.value) return;

  // 如果刚结束拖拽，忽略这次点击
  if (justFinishedDragging.value) {
    justFinishedDragging.value = false;
    return;
  }

  const overlayRect = positionOverlay.value?.getBoundingClientRect();
  if (!overlayRect || !continuousContainer.value) return;

  const clickX = event.clientX - overlayRect.left;
  const clickY = event.clientY - overlayRect.top;

  // 确定点击在哪个页面上
  let targetPageNum: number | null = null;
  let targetCanvas: HTMLCanvasElement | null = null;
  let pageRelativeY = 0;
  let accumulatedHeight = 0;

  for (let pageNum = 0; pageNum < totalPages.value; pageNum++) {
    const pageHeight = pageHeights.value.get(pageNum) || 800;

    if (clickY >= accumulatedHeight && clickY < accumulatedHeight + pageHeight) {
      targetPageNum = pageNum;
      pageRelativeY = clickY - accumulatedHeight;

      // 如果页面已渲染，获取canvas
      if (shouldRenderPage(pageNum)) {
        targetCanvas = pageCanvases.value.get(pageNum) || null;
      }
      break;
    }

    accumulatedHeight += pageHeight;
    // 添加页面间距（除了最后一页）
    if (pageNum < totalPages.value - 1) {
      accumulatedHeight += 20; // margin-bottom
    }
  }

  if (targetPageNum === null) return;

  // 将点击坐标转换为PDF坐标
  const pdfX = Math.round(clickX / scale.value);

  // 如果页面已渲染，使用canvas高度；否则使用预计算的高度
  let pdfY: number;
  if (targetCanvas) {
    pdfY = Math.round((targetCanvas.height - pageRelativeY) / scale.value);
  } else {
    const estimatedHeight = (pageHeights.value.get(targetPageNum) || 800) - 40; // 减去margin
    pdfY = Math.round((estimatedHeight - pageRelativeY) / scale.value);
  }

  // 添加新位置
  const newPosition: [number, number, number] = [targetPageNum, pdfX, pdfY];

  if (editMode.value === 'stamp') {
    allStampPositions.value.push(newPosition);
  } else {
    allSignaturePositions.value.push(newPosition);
  }

  ElMessage.success(`已添加${editMode.value === 'stamp' ? '印章' : '签名'}位置`);
};

// 检测鼠标是否在已有标记上（包括取消按钮区域）
const isMouseOverExistingMarker = (mouseX: number, mouseY: number) => {
  if (!continuousContainer.value) return false;

  // 检查印章标记（包括取消按钮区域）
  for (const stampPos of currentPageStampPositions.value) {
    const [pageNum, x, y] = stampPos.pos;

    const pageWrapper = continuousContainer.value.querySelector(`[data-page-num="${pageNum}"]`) as HTMLElement;
    if (!pageWrapper) continue;

    const canvas = pageWrapper.querySelector('canvas') as HTMLCanvasElement;
    if (!canvas) continue;

    const pageOffsetTop = pageWrapper.offsetTop;
    const canvasX = x * scale.value;
    const canvasY = pageOffsetTop + canvas.height - (y * scale.value);

    // 扩大检测范围，包括取消按钮区域
    const expandedRadius = 64;

    const distance = Math.sqrt(
      (mouseX - canvasX) ** 2 + (mouseY - canvasY) ** 2
    );

    if (distance <= expandedRadius) return true;
  }

  // 检查签名标记（包括取消按钮区域）
  for (const signaturePos of currentPageSignaturePositions.value) {
    const [pageNum, x, y] = signaturePos.pos;

    const pageWrapper = continuousContainer.value.querySelector(`[data-page-num="${pageNum}"]`) as HTMLElement;
    if (!pageWrapper) continue;

    const canvas = pageWrapper.querySelector('canvas') as HTMLCanvasElement;
    if (!canvas) continue;

    const pageOffsetTop = pageWrapper.offsetTop;
    const canvasX = x * scale.value;
    const canvasY = pageOffsetTop + canvas.height - (y * scale.value);

    // 扩大检测范围，包括取消按钮区域
    const expandedRadius = 44;

    const distance = Math.sqrt(
      (mouseX - canvasX) ** 2 + (mouseY - canvasY) ** 2
    );

    if (distance <= expandedRadius) return true;
  }

  return false;
};

// 处理鼠标移动（显示预览）
const handleMouseMove = (event: MouseEvent) => {
  if (dragging.value) {
    // 处理拖拽
    handleDragMove(event);
    return;
  }

  // 如果正在滚动，不显示预览
  if (isScrolling.value) {
    previewPosition.value = null;
    return;
  }

  // 获取鼠标在覆盖层中的位置
  const overlayRect = positionOverlay.value?.getBoundingClientRect();
  if (!overlayRect) return;

  const x = event.clientX - overlayRect.left;
  const y = event.clientY - overlayRect.top;

  // 检测鼠标是否在已有标记上，如果是则不显示预览
  if (isMouseOverExistingMarker(x, y)) {
    previewPosition.value = null;
    return;
  }

  // 显示预览
  previewPosition.value = { x, y };
};

// 处理拖拽移动
const handleDragMove = (event: MouseEvent) => {
  if (!dragging.value || !currentPageProxy.value) return;

  const overlayRect = positionOverlay.value?.getBoundingClientRect();
  if (!overlayRect) return;

  const deltaX = event.clientX - dragging.value.startX;
  const deltaY = event.clientY - dragging.value.startY;

  const newCanvasX = dragging.value.startPosX + deltaX;
  const newCanvasY = dragging.value.startPosY + deltaY;

  // 获取当前页面的视口
  const viewport = currentPageProxy.value.getViewport({ scale: scale.value });

  // 将canvas坐标转换为PDF坐标
  const newPdfX = Math.round(newCanvasX / scale.value);
  const newPdfY = Math.round((viewport.height - newCanvasY) / scale.value);

  // 更新位置
  if (dragging.value.type === 'stamp') {
    const position = allStampPositions.value[dragging.value.index];
    if (position && position[0] === currentPage.value) {
      position[1] = newPdfX;
      position[2] = newPdfY;
    }
  } else {
    const position = allSignaturePositions.value[dragging.value.index];
    if (position && position[0] === currentPage.value) {
      position[1] = newPdfX;
      position[2] = newPdfY;
    }
  }
};

// 隐藏预览
const hidePreview = () => {
  if (!dragging.value) {
    previewPosition.value = null;
  }
};

// 根据滚动位置检测当前实际查看的页面
const detectCurrentPageFromScroll = (container: HTMLElement) => {
  if (!continuousContainer.value) return;

  const scrollTop = container.scrollTop;
  const containerTop = scrollTop + container.clientHeight / 2; // 使用容器中点作为判断基准

  let detectedPage = currentPage.value;
  let accumulatedHeight = 0;

  // 遍历所有页面，根据累积高度判断当前页面
  for (let pageNum = 0; pageNum < totalPages.value; pageNum++) {
    const pageHeight = pageHeights.value.get(pageNum) || 800;

    if (containerTop >= accumulatedHeight && containerTop < accumulatedHeight + pageHeight) {
      detectedPage = pageNum;
      break;
    }

    accumulatedHeight += pageHeight;
    // 添加页面间距（除了最后一页）
    if (pageNum < totalPages.value - 1) {
      accumulatedHeight += 20; // margin-bottom
    }
  }

  // 只有当检测到的页面发生变化时才更新 currentPage
  if (detectedPage !== currentPage.value) {
    currentPage.value = detectedPage;
    updateRenderablePages();
  }
};

// 用户是否正在手动跳转页面（防止滚动检测干扰）
const isManualJumping = ref(false);

// 处理滚动事件
const handleScroll = (event: Event) => {
  const container = event.target as HTMLElement;
  if (!container) return;

  // 滚动时隐藏预览
  isScrolling.value = true;
  previewPosition.value = null;

  // 如果用户正在手动跳转页面，暂时不检测页面变化
  if (!isManualJumping.value) {
    // 检测当前实际查看的页面
    detectCurrentPageFromScroll(container);
  }

  // 清除之前的定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer);
  }

  // 设置新的定时器，滚动结束后恢复预览功能
  scrollTimer = window.setTimeout(() => {
    isScrolling.value = false;
    scrollTimer = null;
  }, 150);
};

// 开始拖拽
const startDrag = (index: number, type: 'stamp' | 'signature', event: MouseEvent) => {
  event.preventDefault();
  event.stopPropagation();

  const positions = type === 'stamp' ? allStampPositions.value : allSignaturePositions.value;
  const currentPagePositions = type === 'stamp' ? currentPageStampPositions.value : currentPageSignaturePositions.value;

  // 找到在当前页面的位置索引对应的原始索引
  const positionOnPage = currentPagePositions[index];
  if (!positionOnPage || !currentPageProxy.value) return;

  const originalIndex = positionOnPage.originalIndex;
  const position = positions[originalIndex];

  // 获取当前像素位置
  const overlayRect = positionOverlay.value?.getBoundingClientRect();
  if (!overlayRect) return;

  // 获取当前页面的视口
  const viewport = currentPageProxy.value.getViewport({ scale: scale.value });

  // 将PDF坐标转换为canvas像素坐标
  const canvasX = position[1] * scale.value;
  const canvasY = viewport.height - (position[2] * scale.value);

  dragging.value = {
    index: originalIndex,
    type,
    startX: event.clientX,
    startY: event.clientY,
    startPosX: canvasX,
    startPosY: canvasY
  };

  document.addEventListener('mousemove', handleDocumentMouseMove);
  document.addEventListener('mouseup', handleDocumentMouseUp);
};

// 文档级鼠标移动
const handleDocumentMouseMove = (event: MouseEvent) => {
  if (!dragging.value) return;
  handleMouseMove(event);
};

// 文档级鼠标释放
const handleDocumentMouseUp = () => {
  if (dragging.value) {
    // 标记刚结束拖拽，防止立即触发点击事件
    justFinishedDragging.value = true;
    // 短时间后重置标记，允许正常点击
    setTimeout(() => {
      justFinishedDragging.value = false;
    }, 100);
  }

  dragging.value = null;
  document.removeEventListener('mousemove', handleDocumentMouseMove);
  document.removeEventListener('mouseup', handleDocumentMouseUp);
};

// 移除位置
const removePosition = (index: number, type: 'stamp' | 'signature') => {
  const currentPagePositions = type === 'stamp' ? currentPageStampPositions.value : currentPageSignaturePositions.value;
  const positionOnPage = currentPagePositions[index];
  if (!positionOnPage) return;

  const originalIndex = positionOnPage.originalIndex;

  if (type === 'stamp') {
    allStampPositions.value.splice(originalIndex, 1);
  } else {
    allSignaturePositions.value.splice(originalIndex, 1);
  }

  ElMessage.success(`已删除${type === 'stamp' ? '印章' : '签名'}位置`);
};

// 清空当前页位置
const clearCurrentPagePositions = () => {
  if (editMode.value === 'stamp') {
    allStampPositions.value = allStampPositions.value.filter(pos => pos[0] !== currentPage.value);
  } else {
    allSignaturePositions.value = allSignaturePositions.value.filter(pos => pos[0] !== currentPage.value);
  }

  ElMessage.success(`已清空当前页${editMode.value === 'stamp' ? '印章' : '签名'}位置`);
};

// 跳转到指定位置
const goToPosition = (pageIndex: number) => {
  goToPage(pageIndex);
};

// 处理关闭
const handleClose = () => {
  // 清理拖拽状态
  if (dragging.value) {
    dragging.value = null;
    document.removeEventListener('mousemove', handleDocumentMouseMove);
    document.removeEventListener('mouseup', handleDocumentMouseUp);
  }

  // 隐藏预览
  previewPosition.value = null;

  // 清理PDF文档
  if (pdfDocument.value) {
    pdfDocument.value.destroy();
    pdfDocument.value = null;
  }

  visible.value = false;
};

// 处理保存
const handleSave = () => {
  emit('save', {
    stamp: allStampPositions.value.map(pos => [pos[0] + 1, pos[1], pos[2]] as [number, number, number]), // 转换回1基索引
    signature: allSignaturePositions.value.map(pos => [pos[0] + 1, pos[1], pos[2]] as [number, number, number])
  });
  visible.value = false;
};

// 监听对话框打开状态，只在打开时初始化一次
watch(() => props.modelValue, async (newValue) => {
  if (newValue && props.pdfData && !pdfDocument.value) {
    // 立即设置加载状态，避免闪烁
    isLoading.value = true;
    // 等待DOM元素准备好
    await nextTick();
    // 增加延迟和错误处理
    setTimeout(async () => {
      if (visible.value && !pdfDocument.value) {
        try {
          await initPdf();
        } catch (error) {
          console.error('PDF初始化失败:', error);
          ElMessage.error('PDF加载失败，请重试');
          isLoading.value = false; // 加载失败时重置状态
        }
      }
    }, 200); // 增加延迟到200ms，确保DOM完全准备好
  } else if (!newValue) {
    // 对话框关闭时清理状态
    previewPosition.value = null;
    isLoading.value = false;

    if (dragging.value) {
      dragging.value = null;
      document.removeEventListener('mousemove', handleDocumentMouseMove);
      document.removeEventListener('mouseup', handleDocumentMouseUp);
    }

    // 清理滚动定时器
    if (scrollTimer) {
      clearTimeout(scrollTimer);
      scrollTimer = null;
    }
    isScrolling.value = false;

    // 清理PDF文档 - 增加安全检查
    if (pdfDocument.value) {
      try {
        pdfDocument.value.destroy();
      } catch (error) {
        console.warn('PDF文档清理时出现错误:', error);
      }
      pdfDocument.value = null;
    }

    // 清理页面代理
    if (currentPageProxy.value) {
      currentPageProxy.value = null;
    }
  }
}, { immediate: false });

// 监听窗口大小变化，重新调整覆盖层
const handleResize = () => {
  adjustOverlaySize();
};

// 组件挂载时的初始化
onMounted(() => {
  window.addEventListener('resize', handleResize);
});

// 组件销毁时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDocumentMouseMove);
  document.removeEventListener('mouseup', handleDocumentMouseUp);
  window.removeEventListener('resize', handleResize);

  // 清理滚动事件监听
  if (pdfContainer.value) {
    pdfContainer.value.removeEventListener('scroll', handleScroll);
  }

  // 清理滚动定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer);
    scrollTimer = null;
  }

  // 清理PDF文档
  if (pdfDocument.value) {
    pdfDocument.value.destroy();
  }
});
</script>

<style scoped>
.pdf-position-editor {
  display: flex;
  flex-direction: column;
  height: 80vh;
}

.editor-toolbar {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid var(--el-border-color);
  flex-wrap: wrap;
}

.tool-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
}

.pdf-container {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  min-height: 0;
  /* 确保flex子项可以收缩 */
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--el-text-color-regular);
}

.loading-icon {
  font-size: 32px;
  color: var(--el-color-primary);
}

.pdf-canvas-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: fit-content;
  min-height: fit-content;
  z-index: 1;
  /* 确保canvas wrapper的层级 */
}

.continuous-canvas-container {
  position: relative;
  width: 100%;
  min-height: 100%;
}

.page-canvas-wrapper {
  position: relative;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.page-canvas-wrapper:last-child {
  margin-bottom: 0;
}

.pdf-canvas {
  max-width: 100%;
  border: 1px solid var(--el-border-color);
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: block;
  position: relative;
  z-index: 1;
}

.page-number {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: var(--el-text-color-regular);
  background: var(--el-bg-color);
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
}

.page-placeholder {
  width: 100%;
  height: 100%;
  border: 2px dashed var(--el-border-color);
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--el-text-color-placeholder);
  position: relative;
}

.page-placeholder .page-number {
  position: static;
  transform: none;
  margin-bottom: 10px;
}

.placeholder-text {
  font-size: 14px;
  opacity: 0.7;
}

.position-overlay {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: auto;
  cursor: crosshair;
  z-index: 2;
  /* 确保覆盖层在canvas之上 */
}

.position-marker {
  position: absolute;
  pointer-events: auto;
  cursor: move;
  z-index: 3;
  /* 确保标记在覆盖层之上 */
}

.stamp-marker {
  width: 80px;
  height: 80px;
}

.signature-marker {
  width: 40px;
  height: 40px;
}

.marker-content {
  width: 100%;
  height: 100%;
  border: 2px solid;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  font-weight: bold;
  position: relative;
}

.stamp-marker .marker-content {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.8);
}

.signature-marker .marker-content {
  border-color: #67c23a;
  background: rgba(103, 194, 58, 0.8);
}

.preview-marker .marker-content {
  border-style: dashed;
  background: rgba(144, 147, 153, 0.6);
}

.marker-number {
  font-size: 10px;
  margin-top: 2px;
}

.remove-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px !important;
  height: 16px !important;
  min-height: 16px !important;
  padding: 0 !important;
  font-size: 8px;
}

.position-summary {
  display: flex;
  gap: 20px;
  padding: 15px;
  border-top: 1px solid var(--el-border-color);
  background: var(--el-bg-color-page);
  max-height: 120px;
  overflow-y: auto;
}

.summary-section {
  flex: 1;
}

.summary-section h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.position-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.position-item {
  padding: 2px 6px;
  font-size: 12px;
  background: var(--el-fill-color-light);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.position-item:hover {
  background: var(--el-color-primary-light-7);
}

.position-item.active {
  background: var(--el-color-primary);
  color: white;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}



@media (max-width: 768px) {
  .pdf-position-editor {
    height: 85vh;
  }

  .editor-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .position-summary {
    flex-direction: column;
    max-height: 100px;
  }

  .stamp-marker {
    width: 60px;
    height: 60px;
  }

  .signature-marker {
    width: 30px;
    height: 30px;
  }

  .pdf-container {
    padding: 10px;
  }

  .pdf-canvas-wrapper {
    min-width: fit-content;
    min-height: fit-content;
  }
}
</style>

<style>
/* PDF位置编辑器对话框样式优化 - 去掉默认边距，减少内边距以显示更多PDF内容 */
.pdf-position-dialog {
  margin-bottom: 16px !important;
  /* 去掉默认的50px下边距 */
}

.pdf-position-dialog .el-dialog__body {
  padding: 0 12px;
}

/* 移动端进一步优化 */
@media (max-width: 768px) {
  .pdf-position-dialog .el-dialog__body {
    padding: 0 !important;
  }

  .pdf-position-dialog .el-dialog__header {
    padding: 12px 15px 8px;
  }
}
</style>
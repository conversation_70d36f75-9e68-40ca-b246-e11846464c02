<script lang="ts">
  import { onMount } from 'svelte'
  import { dashboardStore } from '$stores/dashboard'
  import StatsCard from '$components/StatsCard.svelte'
  import ServerStatusChart from '$components/ServerStatusChart.svelte'
  import RecentDeployments from '$components/RecentDeployments.svelte'
  import SystemHealth from '$components/SystemHealth.svelte'
  import { Server, Activity, Zap, AlertTriangle } from 'lucide-svelte'

  onMount(() => {
    dashboardStore.fetchDashboardData()
  })
</script>

<svelte:head>
  <title>仪表板 - 总控端</title>
</svelte:head>

<div class="space-y-6">
  <!-- 页面标题 -->
  <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
      总控端仪表板
    </h1>
    <p class="mt-2 text-gray-600 dark:text-gray-400">
      统一管理和监控所有服务器和应用
    </p>
  </div>

  <!-- 统计卡片 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <StatsCard
      title="服务器总数"
      value={$dashboardStore.stats.totalServers}
      icon={Server}
      color="blue"
      trend={2}
    />
    
    <StatsCard
      title="在线服务器"
      value={$dashboardStore.stats.onlineServers}
      icon={Activity}
      color="green"
      trend={1}
    />
    
    <StatsCard
      title="部署成功率"
      value="{$dashboardStore.stats.deploymentSuccessRate}%"
      icon={Zap}
      color="purple"
      trend={3}
    />
    
    <StatsCard
      title="告警数量"
      value={$dashboardStore.stats.alertCount}
      icon={AlertTriangle}
      color="red"
      trend={$dashboardStore.stats.alertCount > 0 ? -1 : 0}
    />
  </div>

  <!-- 图表和监控 -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- 服务器状态图表 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        服务器状态分布
      </h2>
      <ServerStatusChart data={$dashboardStore.serverStatus} />
    </div>

    <!-- 系统健康状况 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        系统健康状况
      </h2>
      <SystemHealth data={$dashboardStore.systemHealth} />
    </div>
  </div>

  <!-- 最近部署记录 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
        最近部署记录
      </h2>
    </div>
    <RecentDeployments deployments={$dashboardStore.recentDeployments} />
  </div>
</div>
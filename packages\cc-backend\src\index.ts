import express from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import rateLimit from "express-rate-limit";
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import dotenv from "dotenv";

import { logger } from "./utils/logger";
import { errorHandler } from "./middleware/errorHandler";
import { authMiddleware } from "./middleware/auth";
import { prisma } from "./lib/prisma";
import { redisClient } from "./lib/redis";
import { socketHandler } from "./services/socketService";

// 路由导入
import authRoutes from "./routes/auth";
import serverRoutes from "./routes/servers";
import deploymentRoutes from "./routes/deployments";
import monitoringRoutes from "./routes/monitoring";
import operationRoutes from "./routes/operations";

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 创建HTTP服务器和Socket.IO
const server = createServer(app);
const io = new SocketIOServer(server, {
	cors: {
		origin: process.env.FRONTEND_URL || "http://localhost:5173",
		methods: ["GET", "POST"],
	},
});

// 基础中间件
app.use(
	helmet({
		contentSecurityPolicy: {
			directives: {
				defaultSrc: ["'self'"],
				styleSrc: ["'self'", "'unsafe-inline'"],
				scriptSrc: ["'self'"],
				imgSrc: ["'self'", "data:", "https:"],
			},
		},
	}),
);

app.use(
	cors({
		origin: process.env.FRONTEND_URL || "http://localhost:5173",
		credentials: true,
	}),
);

app.use(compression());

// 速率限制
const limiter = rateLimit({
	windowMs: 15 * 60 * 1000, // 15分钟
	max: 100, // 限制每个IP在窗口期内最多100个请求
	message: {
		error: "请求过于频繁，请稍后再试",
	},
});
app.use("/api", limiter);

app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// 健康检查
app.get("/health", (req, res) => {
	res.json({
		status: "ok",
		timestamp: new Date().toISOString(),
		uptime: process.uptime(),
	});
});

// 公开路由
app.use("/api/auth", authRoutes);

// 需要认证的路由
app.use("/api/servers", authMiddleware, serverRoutes);
app.use("/api/deployments", authMiddleware, deploymentRoutes);
app.use("/api/monitoring", authMiddleware, monitoringRoutes);
app.use("/api/operations", authMiddleware, operationRoutes);

// 错误处理中间件
app.use(errorHandler);

// Socket.IO 处理
socketHandler(io);

// 优雅关闭
const gracefulShutdown = async () => {
	logger.info("开始优雅关闭...");

	server.close(() => {
		logger.info("HTTP 服务器已关闭");
	});

	try {
		await prisma.$disconnect();
		logger.info("数据库连接已关闭");
	} catch (error) {
		logger.error("关闭数据库连接时出错:", error);
	}

	try {
		await redisClient.quit();
		logger.info("Redis 连接已关闭");
	} catch (error) {
		logger.error("关闭 Redis 连接时出错:", error);
	}

	process.exit(0);
};

process.on("SIGTERM", gracefulShutdown);
process.on("SIGINT", gracefulShutdown);

// 启动服务器
server.listen(PORT, () => {
	logger.info(`🚀 总控端后端服务启动成功`);
	logger.info(`📡 服务地址: http://localhost:${PORT}`);
	logger.info(`🌍 环境: ${process.env.NODE_ENV || "development"}`);
});

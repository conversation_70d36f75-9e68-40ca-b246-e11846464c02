import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";

// 创建日志格式
const logFormat = winston.format.combine(
	winston.format.timestamp({
		format: "YYYY-MM-DD HH:mm:ss",
	}),
	winston.format.errors({ stack: true }),
	winston.format.json(),
);

// 控制台格式
const consoleFormat = winston.format.combine(
	winston.format.colorize(),
	winston.format.timestamp({
		format: "YYYY-MM-DD HH:mm:ss",
	}),
	winston.format.printf(({ timestamp, level, message, ...meta }) => {
		return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ""}`;
	}),
);

// 创建日志传输器
const transports: winston.transport[] = [
	// 控制台输出
	new winston.transports.Console({
		format: consoleFormat,
		level: process.env.NODE_ENV === "production" ? "info" : "debug",
	}),

	// 文件输出 - 所有日志
	new DailyRotateFile({
		filename: "logs/application-%DATE%.log",
		datePattern: "YYYY-MM-DD",
		maxSize: "20m",
		maxFiles: "14d",
		format: logFormat,
	}),

	// 文件输出 - 错误日志
	new DailyRotateFile({
		filename: "logs/error-%DATE%.log",
		datePattern: "YYYY-MM-DD",
		level: "error",
		maxSize: "20m",
		maxFiles: "30d",
		format: logFormat,
	}),
];

// 创建 logger 实例
export const logger = winston.createLogger({
	level: process.env.LOG_LEVEL || "info",
	format: logFormat,
	transports,
	// 未捕获异常处理
	exceptionHandlers: [
		new winston.transports.Console({
			format: consoleFormat,
		}),
		new DailyRotateFile({
			filename: "logs/exceptions-%DATE%.log",
			datePattern: "YYYY-MM-DD",
			maxSize: "20m",
			maxFiles: "30d",
			format: logFormat,
		}),
	],
	// 未处理的 Promise 拒绝
	rejectionHandlers: [
		new winston.transports.Console({
			format: consoleFormat,
		}),
		new DailyRotateFile({
			filename: "logs/rejections-%DATE%.log",
			datePattern: "YYYY-MM-DD",
			maxSize: "20m",
			maxFiles: "30d",
			format: logFormat,
		}),
	],
});

// 如果不是生产环境，添加调试级别的日志
if (process.env.NODE_ENV !== "production") {
	logger.add(
		new winston.transports.Console({
			format: consoleFormat,
			level: "debug",
		}),
	);
}

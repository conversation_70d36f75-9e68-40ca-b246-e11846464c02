<template>
  <div class="inquiry-view view">
    <!-- 发起询价板块 -->
    <div class="initiate-inquiry card">
      <div class="card-header">
        <div class="card-title">发起询价</div>
      </div>
      <div class="card-content">
        <div class="inquiry-form">
          <div class="form-label">
            <span class="required-star" v-if="formErrors.includes('inquirySubject')">*</span>
            询价标的：
          </div>
          <div class="form-field">
            <MySelect id="inquirySubject" v-model="formFields.inquirySubject.value" :options="subjectOptions"
              placeholder="选择标的" :loading="isLoadingStocks" :page-size="20" />
          </div>

          <div class="form-label">
            <span class="required-star" v-if="formErrors.includes('inquiryStructure')">*</span>
            询价结构：
          </div>
          <div class="form-field structure-field">
            <div class="structure-group">
              <div class="structure-type">Call</div>
              <div class="structure-options" role="checkboxgroup">
                <label v-for="option in callStructureOptions" :key="option.value" class="call-option">
                  <input type="checkbox" :id="'structure-' + option.value" v-model="formFields.inquiryStructure.value"
                    :value="option.value" name="structure" />
                  {{ option.label.split(' ')[0] }}
                </label>
              </div>
            </div>
            <div class="structure-group">
              <div class="structure-type">Put</div>
              <div class="structure-options" role="checkboxgroup">
                <label v-for="option in putStructureOptions" :key="option.value" class="put-option">
                  <input type="checkbox" :id="'structure-' + option.value" v-model="formFields.inquiryStructure.value"
                    :value="option.value" name="structure" />
                  {{ option.label.split(' ')[0] }}
                </label>
              </div>
            </div>
          </div>

          <div class="form-label">
            <span class="required-star" v-if="formErrors.includes('inquiryScale')">*</span>
            询价规模：
          </div>
          <div class="form-field" role="checkboxgroup">
            <label>
              <input type="checkbox" id="inquiryScale100" v-model="formFields.inquiryScale.value" value="100"
                name="scale" />
              100 万
            </label>
            <label>
              <input type="checkbox" id="inquiryScale200" v-model="formFields.inquiryScale.value" value="200"
                name="scale" />
              200 万
            </label>
            <label>
              <input type="checkbox" id="inquiryScale300" v-model="formFields.inquiryScale.value" value="300"
                name="scale" />
              300 万
            </label>
          </div>

          <div class="form-label">
            <span class="required-star" v-if="formErrors.includes('inquiryTerm')">*</span>
            询价期限：
          </div>
          <div class="form-field" role="checkboxgroup">
            <label>
              <input type="checkbox" id="inquiryTerm14" v-model="formFields.inquiryTerm.value" value="14" name="term" />
              2 周
            </label>
            <label>
              <input type="checkbox" id="inquiryTerm1" v-model="formFields.inquiryTerm.value" value="1" name="term" />
              1 个月
            </label>
            <label>
              <input type="checkbox" id="inquiryTerm2" v-model="formFields.inquiryTerm.value" value="2" name="term" />
              2 个月
            </label>
            <label>
              <input type="checkbox" id="inquiryTerm3" v-model="formFields.inquiryTerm.value" value="3" name="term" />
              3 个月
            </label>
          </div>
        </div>
        <div class="error-message" v-if="formErrors.length">请完成必选项</div>
        <div class="button-group">
          <span class="validity-hint">
            <el-icon>
              <InfoFilled />
            </el-icon>
            询价有效期为30分钟
          </span>
          <button class="secondary-button" @click="resetForm">重置</button>
          <button class="primary-button" @click="initiateInquiry" :disabled="isInquirySubmitting">发起询价</button>
        </div>
      </div>
    </div>

    <!-- 今日询价板块 -->
    <div class="today-inquiries card">
      <div class="card-header">
        <div class="card-title">今日询价</div>
      </div>
      <div class="filter-row">
        <div class="form-group">
          <label for="selectedSubject">选择标的：</label>
          <MySelect id="selectedSubject" v-model="selectedSubject" :options="inquirySubjectOptions" placeholder="全部"
            :page-size="20" />
        </div>
      </div>
      <TableWrapper sort-label="今日询价表格" v-model:page-size="pageSize" v-model:current-page="currentPage"
        v-model:is-descending="isDescending" :total-pages="totalPages">
        <LoadingState v-if="!paginatedInquiries.length" :loading="isLoading" :has-data="paginatedInquiries.length > 0"
          :icon="DataLine" :overlay="true" />
        <template v-else>
          <div class="table-container">
            <LoadingState :loading="isLoading" :has-data="paginatedInquiries.length > 0" :icon="DataLine"
              :class="['loading-overlay', { 'visible': isLoading }]" />
            <table class="data-table">
              <thead>
                <tr>
                  <th>标的</th>
                  <th class="desktop-only">询价规模</th>
                  <th class="desktop-only">结构</th>
                  <th class="desktop-only">期限</th>
                  <th :class="{ 'desktop-only': true, 'quote-column': true }">
                    报价</th>
                  <th>期权费</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="inquiry in paginatedInquiries" :key="inquiry.inquiry_id">
                  <tr class="order-row" :class="{ 'selected': selectedInquiry?.inquiry_id === inquiry.inquiry_id }"
                    @click="toggleInquiryDetails(inquiry)">
                    <td>{{ formatSubject(inquiry.subject) }}</td>
                    <td class="desktop-only">{{ inquiry.scale + "万" }}</td>
                    <td class="desktop-only">{{ formatStructure(inquiry.structure) }}</td>
                    <td class="desktop-only">{{ inquiry.term === 14 ? '2周' : inquiry.term + "个月" }}</td>
                    <td :class="{ 'desktop-only': true, 'quote-column': true }">
                      <div v-if="inquiry.status === InquiryStatus.REJECTED" class="no-quote">无报价</div>
                      <template v-else>
                        <div class="quote-value" @click.stop="toggleQuoteDetails(inquiry)">
                          <div class="quote-content">
                            <span class="provider-tag">{{
                              getProviderName(selectedQuotes[inquiry.inquiry_id]?.provider
                                || 'INK') }}</span>
                            <span>{{ getQuoteText(inquiry) }}</span>
                          </div>
                          <el-icon class="expand-icon" :class="{ 'is-expanded': expandedQuotes[inquiry.inquiry_id] }">
                            <ArrowDown />
                          </el-icon>
                        </div>
                        <transition name="expand">
                          <div v-if="expandedQuotes[inquiry.inquiry_id]" class="quote-details">
                            <div v-for="item in getSortedQuotes(inquiry)" :key="item.provider" class="quote-row"
                              v-show="!item.isRejected" :class="{
                                'is-selected': selectedQuotes[inquiry.inquiry_id]?.provider === item.provider,
                                'is-optimal': item.isOptimal
                              }" @click.stop="selectQuote(inquiry, item.provider, item.price)">
                              <span class="provider" :class="{ 'internal': item.provider === 'INK' }">
                                {{ getProviderName(item.provider) }}
                                <span class="region-tag">{{ ['INK', 'HAIYING'].includes(item.provider) ? 'HK' : 'CN'
                                }}</span>
                                <span v-if="getTradingRestriction(item.provider) !== undefined" class="trading-tag">T+{{
                                  getTradingRestriction(item.provider) }}</span>
                              </span>
                              <span class="price">{{ item.adjustedPrice.toFixed(2) }}%</span>
                            </div>
                          </div>
                        </transition>
                      </template>
                    </td>
                    <td>{{ inquiry.status === InquiryStatus.REJECTED ? '-' :
                      formatOptionFee(inquiry) + "万" }}</td>
                    <td>
                      <el-button class="action-button" @click.stop="handleInquiry(inquiry)"
                        :disabled="isActionButtonDisabled(inquiry)">
                        {{ getButtonText(inquiry) }}
                      </el-button>
                    </td>
                  </tr>
                  <template v-if="selectedInquiry?.inquiry_id === inquiry.inquiry_id">
                    <tr class="mobile-only details-row">
                      <td colspan="4">
                        <div class="order-details details-transition">
                          <div class="detail-item">
                            <span class="label">询价规模：</span>
                            <span class="value">{{ inquiry.scale + "万" }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">结构：</span>
                            <span class="value">{{ formatStructure(inquiry.structure) }}</span>
                          </div>
                          <div class="detail-item">
                            <span class="label">期限：</span>
                            <span class="value">{{ inquiry.term === 14 ? '2周' : inquiry.term + "个月" }}</span>
                          </div>
                          <div class="detail-item quote-details-mobile">
                            <span class="label">报价：</span>
                            <div v-if="inquiry.status === InquiryStatus.REJECTED" class="value no-quote">无报价</div>
                            <div v-else class="value">
                              <div class="quote-value-mobile" @click.stop="toggleQuoteDetails(inquiry)">
                                <div class="quote-content">
                                  <span class="provider-tag">{{
                                    getProviderName(selectedQuotes[inquiry.inquiry_id]?.provider || 'INK')
                                  }}</span>
                                  <span>{{ getQuoteText(inquiry) }}</span>
                                </div>
                                <el-icon class="expand-icon"
                                  :class="{ 'is-expanded': expandedQuotes[inquiry.inquiry_id] }">
                                  <ArrowDown />
                                </el-icon>
                              </div>
                              <transition name="expand">
                                <div v-if="expandedQuotes[inquiry.inquiry_id]" class="quote-details-mobile-inner">
                                  <div v-for="item in getSortedQuotes(inquiry)" :key="item.provider" class="quote-row"
                                    v-show="!item.isRejected" :class="{
                                      'is-selected': selectedQuotes[inquiry.inquiry_id]?.provider === item.provider,
                                      'is-optimal': item.isOptimal
                                    }" @click.stop="selectQuote(inquiry, item.provider, item.price)">
                                    <span class="provider" :class="{ 'internal': item.provider === 'INK' }">
                                      {{ getProviderName(item.provider) }}
                                      <span class="region-tag">{{ ['INK', 'HAIYING'].includes(item.provider) ? 'HK' :
                                        'CN' }}</span>
                                      <span v-if="getTradingRestriction(item.provider) !== undefined"
                                        class="trading-tag">T+{{ getTradingRestriction(item.provider) }}</span>
                                    </span>
                                    <span class="price">{{ item.adjustedPrice.toFixed(2) }}%</span>
                                  </div>
                                </div>
                              </transition>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                    <tr class="dummy-row"></tr>
                  </template>
                </template>
              </tbody>
            </table>
          </div>
        </template>
      </TableWrapper>
    </div>

    <!-- 下单确认模态框 -->
    <OrderConfirmModal v-model="showOrderModal" :inquiry="currentInquiry"
      :selected-provider="currentInquiry ? selectedQuotes[currentInquiry.inquiry_id]?.provider : undefined"
      @confirm="handleOrderConfirm" />

    <!-- 录单模态框 -->
    <ManualOrderModal v-model="showManualOrderModal" :order-data="currentManualOrder"
      @submit="handleManualOrderSubmit" />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  watch,
  onMounted,
  computed,
  onUnmounted,
  onActivated,
} from "vue";
import { storeToRefs } from "pinia";
import MySelect from "@/components/MySelect.vue";
import { inquiryApi, tradeApi, manualOrderApi } from "@/api";
import OrderConfirmModal from "./modals/OrderConfirmModal.vue";
import TableWrapper from "@/components/TableWrapper.vue";
import type {
  QuoteResponse,
  InquiryItem,
  ExternalQuoteProvider,
  ManualOrderRequest,
} from "@packages/shared";
import {
  StructureValue,
  StructureType,
  InquiryStatus,
  PriceProviderNames,
  ProviderTradingRestrictions,
  OrderType,
  TradeDirection,
  BuyRequest,
  getErrorMessage,
  QUOTE_REJECTED
} from "@packages/shared";
import { ElMessage } from "element-plus";
import { DataLine, InfoFilled, ArrowDown } from "@element-plus/icons-vue";
import LoadingState from "@/components/LoadingState.vue";
import { useTableSettings } from "@/composables/useTableSettings";
import { formatStructure } from "@/utils/format";
import { systemStatusManager } from "@/core/systemStatusManager";
import { useStockStore } from "@/stores/stock";
import { useSiteConfigStore } from "@/stores/siteConfig";
import { useSharedConfigStore } from "@/stores/sharedConfig";
import ManualOrderModal from "./modals/ManualOrderModal.vue";

const siteConfigStore = useSiteConfigStore();
const sharedConfigStore = useSharedConfigStore();
const stockStore = useStockStore();

// 使用 storeToRefs 解构响应式状态
const { config } = storeToRefs(sharedConfigStore);

// 简化的 isExternalOrderEnabled 计算属性
const isExternalOrderEnabled = computed(() =>
  config.value?.channel_management?.enable_external_order ?? false
);

const isLoading = ref(false);

// Form State
const formFields = reactive({
  inquirySubject: { value: [] as string[], required: true },
  inquiryStructure: { value: [] as StructureType[], required: true },
  inquiryScale: { value: [] as string[], required: true },
  inquiryTerm: { value: [] as string[], required: true },
});
const formErrors = ref<string[]>([]);

// Stock Search State
const isLoadingStocks = computed(() => !stockStore.isInitialized);

// Inquiry List State
const selectedSubject = ref<string[]>([]);
const todayInquiries = ref<InquiryItem[]>([]);

// Table Settings
const { initSettings, pageSize, isDescending, currentPage, totalPages } =
  useTableSettings();

// 下单确认相关状态
const showOrderModal = ref(false);
const currentInquiry = ref<InquiryItem | null>(null);

// 录单相关状态
const showManualOrderModal = ref(false);
const currentManualOrder = ref<ManualOrderRequest | null>(null);

// 询价状态管理
interface InquiryTimer {
  inquiryId: string;
  timerId: number;
}

const inquiryTimers = ref<InquiryTimer[]>([]);

// 设置询价的状态更新定时器
const setupInquiryTimer = (inquiry: InquiryItem) => {
  if (inquiry.status !== InquiryStatus.APPROVED) return;

  const createdAt = new Date(inquiry.created_at).getTime();
  const now = Date.now();
  const timeLeft = 30 * 60 * 1000 - (now - createdAt);

  if (timeLeft <= 0) return;

  // 设置过期定时器
  const expiryTimerId = window.setTimeout(() => {
    const index = todayInquiries.value.findIndex(
      (i) => i.inquiry_id === inquiry.inquiry_id,
    );
    if (index !== -1) {
      todayInquiries.value[index] = {
        ...todayInquiries.value[index],
        status: InquiryStatus.EXPIRED,
      };
    }
  }, timeLeft);

  inquiryTimers.value.push({
    inquiryId: inquiry.inquiry_id.toString(),
    timerId: expiryTimerId,
  });

  // 如果剩余时间大于3分钟，设置警告定时器
  if (timeLeft > 3 * 60 * 1000) {
    const warningTimerId = window.setTimeout(
      () => {
        // 触发UI更新
        todayInquiries.value = [...todayInquiries.value];
      },
      timeLeft - 3 * 60 * 1000,
    );

    inquiryTimers.value.push({
      inquiryId: inquiry.inquiry_id.toString(),
      timerId: warningTimerId,
    });
  }
};

// 清理询价定时器
const clearInquiryTimer = (inquiryId: string) => {
  const timers = inquiryTimers.value.filter((t) => t.inquiryId === inquiryId);
  for (const t of timers) {
    window.clearTimeout(t.timerId);
  }
  inquiryTimers.value = inquiryTimers.value.filter(
    (t) => t.inquiryId !== inquiryId,
  );
};

// 初始化所有询价定时器
const initializeInquiryTimers = () => {
  // 清理现有定时器
  for (const t of inquiryTimers.value) {
    window.clearTimeout(t.timerId);
  }
  inquiryTimers.value = [];

  // 为每个有效询价设置定时器
  for (const inquiry of todayInquiries.value) {
    setupInquiryTimer(inquiry);
  }
};

// 监听询价列表变化
watch(todayInquiries, (newInquiries, oldInquiries) => {
  if (!oldInquiries) {
    // 初始化时设置所有定时器
    initializeInquiryTimers();
    return;
  }

  // 处理新增的询价
  for (const inquiry of newInquiries) {
    const oldInquiry = oldInquiries.find(
      (i) => i.inquiry_id === inquiry.inquiry_id,
    );
    if (!oldInquiry) {
      setupInquiryTimer(inquiry);
    }
  }

  // 清理已移除的询价定时器
  for (const inquiry of oldInquiries) {
    if (!newInquiries.find((i) => i.inquiry_id === inquiry.inquiry_id)) {
      clearInquiryTimer(inquiry.inquiry_id.toString());
    }
  }
});

// 组件卸载时清理所有定时器
onUnmounted(() => {
  for (const t of inquiryTimers.value) {
    window.clearTimeout(t.timerId);
  }
  inquiryTimers.value = [];
});

// 添加新的响应式变量存储所有可用的标的代码
const availableTsCodes = ref<string[]>([]);

// 修改标的选项计算属性
const inquirySubjectOptions = computed(() => {
  if (!availableTsCodes.value) return [];

  return availableTsCodes.value.map((ts_code) => ({
    value: ts_code,
    label: formatSubject(ts_code),
  }));
});

const filteredInquiries = computed(() => {
  let filtered = [...todayInquiries.value];

  // 只保留主题筛选
  if (selectedSubject.value?.length > 0) {
    filtered = filtered.filter((inquiry) =>
      selectedSubject.value.includes(inquiry.subject),
    );
  }

  return filtered;
});

const paginatedInquiries = computed(() => filteredInquiries.value);

// 修改结构选项的计算属性，分为看涨和看跌两组
const callStructureOptions = computed(() =>
  Object.entries(StructureValue)
    .filter(([value]) => value.includes("C"))
    .map(([value, label]) => ({
      value: value as StructureType,
      label,
    })),
);

const putStructureOptions = computed(() =>
  Object.entries(StructureValue)
    .filter(([value]) => value.includes("P"))
    .map(([value, label]) => ({
      value: value as StructureType,
      label,
    })),
);

// 修改 subjectOptions 的计算属性
const subjectOptions = computed(() => {
  // 如果不允许询价，直接返回空数组
  if (!systemStatusManager.isInquiryEnabled.value) {
    return [];
  }

  if (!stockStore.isInitialized) {
    return [];
  }

  // 获取所有标的并过滤掉名称中包含 "ST" 的股票
  const allSubjects = stockStore.getAllFormattedSubjects().filter((subject) => {
    const stockName = stockStore.getStockName(subject.value);
    return !stockName.includes("ST");
  });

  // 如果没有最近查询记录，直接返回所有标的
  if (!lastInquiredSubject.value) {
    return allSubjects;
  }

  // 检查上次查询的标的是否在可用列表中
  const lastSubjectIndex = allSubjects.findIndex(
    (s) => s.value === lastInquiredSubject.value,
  );
  if (lastSubjectIndex === -1) {
    return allSubjects;
  }

  // 把上次查询的标的放到第一位
  const result = [...allSubjects];
  const lastSubject = result.splice(lastSubjectIndex, 1)[0];
  result.unshift(lastSubject);

  return result;
});

// 只保存上一次查询的标的
const LAST_SUBJECT_KEY = "INK_LAST_SUBJECT";
const lastInquiredSubject = ref<string | null>(null);

// Form Methods
const validateForm = () => {
  const errors: string[] = [];
  for (const [key, field] of Object.entries(formFields)) {
    if (
      field.required &&
      (!field.value || (Array.isArray(field.value) && field.value.length === 0))
    ) {
      errors.push(key);
    }
  }
  return errors;
};

const resetForm = () => {
  for (const key of Object.keys(formFields)) {
    formFields[key as keyof typeof formFields].value = []; // Always reset to empty array
  }
};

// 避免询价双击重复提交
const isInquirySubmitting = ref(false);

const initiateInquiry = async () => {
  if (isInquirySubmitting.value) return;
  formErrors.value = validateForm();
  if (formErrors.value.length > 0) return;

  try {
    isInquirySubmitting.value = true;

    // 记录查询的标的
    if (formFields.inquirySubject.value.length > 0) {
      lastInquiredSubject.value = formFields.inquirySubject.value[0];
      localStorage.setItem(LAST_SUBJECT_KEY, lastInquiredSubject.value);
    }

    // 构建批量询价请求 - 修改为处理多选组合
    const quoteRequests = formFields.inquirySubject.value.flatMap((subject) =>
      formFields.inquiryStructure.value.flatMap((structure) =>
        formFields.inquiryScale.value.flatMap((scale) =>
          formFields.inquiryTerm.value.map((term) => ({
            ts_code: subject,
            structure: structure,
            scale: Number.parseInt(scale),
            term: Number.parseInt(term),
          })),
        ),
      ),
    );

    const response = await inquiryApi.createBatchQuotes({
      quotes: quoteRequests,
    });
    await loadInquiries();
    resetForm();

    // 获取被拒绝的标的代码并去重
    const rejectedTsCodes = response
      ? [
        ...new Set(
          response
            .filter((inquiry) => inquiry.status === InquiryStatus.REJECTED)
            .map((inquiry) => inquiry.ts_code),
        ),
      ]
      : [];

    ElMessage.success("询价成功");
    if (rejectedTsCodes.length > 0) {
      ElMessage.warning({
        dangerouslyUseHTMLString: true,
        duration: 6000,
        message: `以下标的不符合报价条件或超出规模限制：<br><br>${rejectedTsCodes.map((ts_code) => formatSubject(ts_code)).join("，")}`,
      });
    }
  } catch (error) {
    const errorMessage = getErrorMessage(error);
    if (errorMessage) {
      ElMessage.error(errorMessage);
    } else {
      ElMessage.error("询价失败");
    }
    console.error("Failed to create quotes:", error);
  } finally {
    isInquirySubmitting.value = false;
  }
};

// Inquiry List Methods
const loadInquiries = async () => {
  try {
    isLoading.value = true;
    // 重置所有报价展开状态
    expandedQuotes.value = {};

    const response = await inquiryApi.getInquiries(
      currentPage.value,
      pageSize.value,
      isDescending.value,
      {
        ts_codes: selectedSubject.value,
      },
    );
    todayInquiries.value =
      response?.items.map(
        (inquiry: QuoteResponse): InquiryItem => ({
          ...inquiry,
          subject: inquiry.ts_code,
        }),
      ) || [];

    // 初始化所有询价的选中报价
    for (const inquiry of todayInquiries.value) {
      initSelectedQuote(inquiry);
    }

    totalPages.value = Math.ceil((response?.total || 1) / pageSize.value);
    // 更新可用标的列表
    availableTsCodes.value = response?.ts_codes || [];
  } catch (error) {
    console.error("Failed to load inquiries:", error);
  } finally {
    isLoading.value = false;
  }
};

for (const key of Object.keys(formFields)) {
  watch(
    () => formFields[key as keyof typeof formFields].value,
    () => {
      formErrors.value = [];
    },
  );
}

// Lifecycle Hooks
onActivated(() => {
  // 只在数据为空时加载数据
  if (todayInquiries.value.length === 0) {
    loadInquiries();
  }
});

onMounted(() => {
  initSettings();
  document.addEventListener("click", handleDocumentClick);

  // 加载上次查询的标的
  lastInquiredSubject.value = localStorage.getItem(LAST_SUBJECT_KEY);
});

// 监听筛选变化
watch([selectedSubject], () => {
  currentPage.value = 1; // 重置到第一页
  loadInquiries(); // 重新获取数据
  console.log("loadInquiries at watch");
});

// 监听分页和排序变化
watch([currentPage, pageSize, isDescending], () => {
  loadInquiries();
  console.log("loadInquiries at watch");
});



// 监听录单模态框关闭，清理状态
watch(showManualOrderModal, (isOpen) => {
  if (!isOpen) {
    currentManualOrder.value = null;
  }
});

// 修改为记录正在下单的询价编号
const submittingInquiryId = ref(0);

// 创建独立的按钮禁用判断函数
const isActionButtonDisabled = (inquiry: InquiryItem): boolean => {
  // 基本状态检查
  if (
    inquiry.status === InquiryStatus.REJECTED ||
    inquiry.status === InquiryStatus.EXPIRED ||
    inquiry.status === InquiryStatus.APPROVED
  ) {
    return true;
  }

  // 如果是录单按钮，只检查基本状态和提交状态
  const selectedProvider = selectedQuotes.value[inquiry.inquiry_id]?.provider;
  if (!isExternalOrderEnabled.value && selectedProvider && selectedProvider !== "INK") {
    return submittingInquiryId.value === inquiry.inquiry_id;
  }

  // 下单按钮需要检查系统状态
  return (
    !systemStatusManager.isSystemEnabled.value ||
    !systemStatusManager.isPositionEntryEnabled.value ||
    submittingInquiryId.value === inquiry.inquiry_id
  );
};

// 更新处理询价的方法
const handleInquiry = (inquiry: InquiryItem) => {
  // 如果禁用了外部下单功能且当前选择了外部报价，执行录单操作
  const selectedProvider = selectedQuotes.value[inquiry.inquiry_id]?.provider;
  if (!isExternalOrderEnabled.value && selectedProvider && selectedProvider !== "INK") {
    // 准备录单数据，只传递基本信息，让录单模态框自己处理自动填充
    const quoteDiff = inquiry.quote_diffs?.[selectedProvider] || 0;
    const adjustedQuote = (selectedQuotes.value[inquiry.inquiry_id]?.price || 0) + quoteDiff;

    currentManualOrder.value = {
      ts_code: inquiry.subject,
      structure: inquiry.structure,
      scale: inquiry.scale,
      term: inquiry.term,
      quote_provider: getProviderName(selectedProvider),
      quote: adjustedQuote,
    } as ManualOrderRequest;

    showManualOrderModal.value = true;
    return;
  }

  // 检查是否禁用了外部下单功能
  if (!isExternalOrderEnabled.value && inquiry.external_quotes) {
    const selectedProvider = selectedQuotes.value[inquiry.inquiry_id]?.provider;
    // 如果选择了非INK的外部报价，则显示提示信息
    if (selectedProvider && selectedProvider !== "INK") {
      ElMessage.warning("外部报价下单功能已被禁用，请选择平台报价");
      return;
    }
  }

  currentInquiry.value = inquiry;
  showOrderModal.value = true;
};

// 添加录单提交处理函数
const handleManualOrderSubmit = async (data: ManualOrderRequest) => {
  try {
    // 调用API创建录单
    await manualOrderApi.createManualOrder(data);

    showManualOrderModal.value = false;
    currentManualOrder.value = null;
    ElMessage.success("录单创建成功");
  } catch (error) {
    console.error("Failed to create manual order:", error);
    ElMessage.error("录单创建失败");
  }
};

// 处理订单确认的方法
const handleOrderConfirm = async (orderType: string, limitPrice?: number) => {
  if (!currentInquiry.value) return;

  try {
    submittingInquiryId.value = currentInquiry.value.inquiry_id;
    // 获取选中的报价信息
    const selectedQuote = selectedQuotes.value[currentInquiry.value.inquiry_id];

    if (!selectedQuote) {
      ElMessage.error("未选择报价");
      return;
    }

    const quoteDiff =
      currentInquiry.value.quote_diffs?.[selectedQuote.provider] || 0;
    const adjustedPrice = selectedQuote.price + quoteDiff;

    let orderRequest: BuyRequest = {
      type: orderType as OrderType,
      direction: TradeDirection.BUY,
      user_id: 0, // 将由后端从 session 获取
      inquiry_id: currentInquiry.value.inquiry_id,
      ts_code: currentInquiry.value.subject,
      scale: currentInquiry.value.scale,
      term: currentInquiry.value.term,
      structure: currentInquiry.value.structure,
      quote: adjustedPrice, // 使用调整后报价
      quote_provider: selectedQuote.provider, // 添加报价提供商信息
      quote_diff: quoteDiff, // 添加报价差价信息
    };

    // 验证限价单的价格
    if (orderType === OrderType.LIMIT) {
      if (!limitPrice || limitPrice <= 0) {
        ElMessage.error("限价价格无效");
        return;
      }
      orderRequest = {
        ...orderRequest,
        limit_price: limitPrice,
      };
    }

    await tradeApi.createOrder(orderRequest);
    ElMessage.success("下单成功");

    // 成功后立即关闭确认框
    currentInquiry.value = null;
    showOrderModal.value = false;

    // 重新加载询价列表以反映最新状态
    await loadInquiries();

    // 关闭任何打开的报价详情
    for (const key of Object.keys(expandedQuotes.value)) {
      expandedQuotes.value[Number(key)] = false;
    }
  } catch (error) {
    console.error("Failed to create order:", error);
    ElMessage.error(`下单失败：${getErrorMessage(error)}`);

    // 下单失败时刷新询价数据，但不关闭确认框
    await loadInquiries();

    // 更新当前选中的询价数据
    if (currentInquiry.value) {
      const updatedInquiry = todayInquiries.value.find(
        (i) => i.inquiry_id === currentInquiry.value?.inquiry_id,
      );
      if (updatedInquiry) {
        currentInquiry.value = updatedInquiry;
      }
    }
  } finally {
    submittingInquiryId.value = 0;
  }
};

// 计算期权费的函数
const formatOptionFee = (inquiry: InquiryItem) => {
  // 初始化选中报价（如果尚未初始化）
  initSelectedQuote(inquiry);
  const selectedQuote = selectedQuotes.value[inquiry.inquiry_id];

  // 检查选中报价是否有效
  if (
    !selectedQuote ||
    selectedQuote.price === QUOTE_REJECTED ||
    selectedQuote.price === null
  ) {
    return "0.00"; // 报价无效时显示0
  }

  // 使用调整后的报价计算期权费
  const priceDiff = inquiry.quote_diffs?.[selectedQuote.provider] || 0;
  const adjustedPrice =
    Math.round((selectedQuote.price + priceDiff) * 100) / 100;

  const optionFee = inquiry.scale * (adjustedPrice / 100); // 单位：万
  return new Intl.NumberFormat("zh-CN", {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(optionFee);
};

// 获取按钮文本的函数
const getButtonText = (inquiry: InquiryItem) => {
  if (inquiry.status === InquiryStatus.REJECTED) {
    return "已拒绝";
  }
  if (inquiry.status === InquiryStatus.EXPIRED) {
    return "已过期";
  }
  if (inquiry.status === InquiryStatus.APPROVED) {
    return "已使用";
  }

  // 如果是外部报价且外部报价下单功能被禁用，显示为"录单"按钮
  const selectedProvider = selectedQuotes.value[inquiry.inquiry_id]?.provider;
  if (!isExternalOrderEnabled.value && selectedProvider && selectedProvider !== "INK") {
    return "录单";
  }

  return "下单";
};

// 使用 store 的 formatSubject
const { formatSubject } = stockStore;

// 在 script setup 中添加
const selectedInquiry = ref<InquiryItem | null>(null);

const toggleInquiryDetails = (inquiry: InquiryItem) => {
  if (selectedInquiry.value?.inquiry_id === inquiry.inquiry_id) {
    selectedInquiry.value = null;
  } else {
    selectedInquiry.value = inquiry;
  }
};

// 添加报价详情展开状态
const expandedQuotes = ref<Record<number, boolean>>({});

// 切换报价详情展开状态
const toggleQuoteDetails = (inquiry: InquiryItem, event?: Event) => {
  // 如果当前询价已经展开，则关闭它
  if (expandedQuotes.value[inquiry.inquiry_id]) {
    expandedQuotes.value[inquiry.inquiry_id] = false;
  } else {
    // 如果当前询价未展开，先关闭所有展开的询价，再展开当前询价
    for (const key of Object.keys(expandedQuotes.value)) {
      expandedQuotes.value[Number(key)] = false;
    }
    expandedQuotes.value[inquiry.inquiry_id] = true;
  }
  // 添加防止事件冒泡的代码
  event?.stopPropagation();
};

// 添加点击外部关闭所有报价详情功能
const handleDocumentClick = (event: MouseEvent) => {
  // 检查点击是否发生在报价区域外
  const quoteElements = document.querySelectorAll(
    ".quote-value, .quote-value-mobile, .quote-details, .quote-details-mobile-inner",
  );
  let clickedInside = false;

  for (const element of Array.from(quoteElements)) {
    if (element.contains(event.target as Node)) {
      clickedInside = true;
      break;
    }
  }

  if (!clickedInside) {
    // 关闭所有展开的报价
    for (const key of Object.keys(expandedQuotes.value)) {
      expandedQuotes.value[Number(key)] = false;
    }
  }
};

// 获取提供商中文名
const getProviderName = (provider: string): string => {
  if (provider === "INK") return siteConfigStore.shortName();
  return PriceProviderNames[provider as ExternalQuoteProvider] || provider;
};

// 获取报价显示文本
const getQuoteText = (inquiry: InquiryItem): string => {
  // 初始化选中报价（如果尚未初始化）
  initSelectedQuote(inquiry);
  const selectedQuote = selectedQuotes.value[inquiry.inquiry_id];

  // 检查选中报价是否有效
  if (
    !selectedQuote ||
    selectedQuote.price === QUOTE_REJECTED ||
    selectedQuote.price === null
  ) {
    return "无报价";
  }

  // 使用对应提供商的报价差价
  const priceDiff = inquiry.quote_diffs?.[selectedQuote.provider] || 0;
  const adjustedPrice =
    Math.round((selectedQuote.price + priceDiff) * 100) / 100;
  return `${adjustedPrice.toFixed(2)}%`;
};

// 修改排序报价函数，确保标记最优报价
const getSortedQuotes = (inquiry: InquiryItem) => {
  // 创建结果数组
  const quotes: {
    provider: string;
    price: number | null;
    adjustedPrice: number;
    isOptimal: boolean;
    isRejected: boolean;
  }[] = [];

  // 跟踪最优报价
  let optimalPrice = Number.MAX_VALUE;
  let optimalProvider = "";

  // 处理内部报价
  const isInkRejected = inquiry.quote === QUOTE_REJECTED;
  const priceDiffInk = inquiry.quote_diffs?.INK || 0;

  if (!isInkRejected) {
    // 内部报价有效，计算调整后价格并检查是否是最优报价
    const adjustedInkPrice =
      Math.round((inquiry.quote + priceDiffInk) * 100) / 100;
    optimalPrice = adjustedInkPrice;
    optimalProvider = "INK";

    // 添加到结果数组
    quotes.push({
      provider: "INK",
      price: inquiry.quote,
      adjustedPrice: adjustedInkPrice,
      isOptimal: true, // 先假设是最优，后续会更新
      isRejected: false,
    });
  } else {
    // 内部报价被拒绝，仍然添加但标记为被拒绝
    quotes.push({
      provider: "INK",
      price: QUOTE_REJECTED,
      adjustedPrice: 0, // 使用0是因为已经标记为isRejected，不会用于显示
      isOptimal: false,
      isRejected: true,
    });
  }

  // 处理外部报价
  if (inquiry.external_quotes && inquiry.quote_diffs) {
    for (const [provider, price] of Object.entries(inquiry.external_quotes)) {
      // 检查外部报价是否有效
      if (price !== null && price > 0) {
        const priceDiff = inquiry.quote_diffs[provider] || 0;
        const adjustedPrice = Math.round((price + priceDiff) * 100) / 100;

        // 检查是否比当前最优报价更好
        const isCurrentOptimal = adjustedPrice < optimalPrice;
        if (isCurrentOptimal) {
          optimalPrice = adjustedPrice;
          optimalProvider = provider;
        }

        // 添加到结果数组
        quotes.push({
          provider,
          price,
          adjustedPrice,
          isOptimal: isCurrentOptimal, // 先设置，后续会更新
          isRejected: false,
        });
      } else {
        // 无效的外部报价，标记为被拒绝
        quotes.push({
          provider,
          price: null,
          adjustedPrice: 0,
          isOptimal: false,
          isRejected: true,
        });
      }
    }
  }

  // 更新最优报价标记
  for (const quote of quotes) {
    quote.isOptimal = quote.provider === optimalProvider;
  }

  // 按调整后价格从低到高排序，将被拒绝的报价排在最后
  return quotes.sort((a, b) => {
    // 被拒绝的报价排在最后
    if (a.isRejected && !b.isRejected) return 1;
    if (!a.isRejected && b.isRejected) return -1;
    // 两者都未被拒绝，按价格排序
    if (!a.isRejected && !b.isRejected) {
      return a.adjustedPrice - b.adjustedPrice;
    }
    // 两者都被拒绝，顺序不重要
    return 0;
  });
};

// 首先添加一个跟踪选中报价的状态
const selectedQuotes = ref<Record<number, { provider: string; price: number }>>(
  {},
);

// 初始化选中报价的函数
const initSelectedQuote = (inquiry: InquiryItem) => {
  if (!selectedQuotes.value[inquiry.inquiry_id]) {
    // 找出调整后价格最低的报价提供商
    const sortedQuotes = getSortedQuotes(inquiry);

    // 过滤掉被拒绝的报价
    const validQuotes = sortedQuotes.filter(
      (q) => !q.isRejected && q.price !== null && q.price !== QUOTE_REJECTED,
    );

    if (validQuotes.length > 0) {
      // 有有效的报价，选择最优的
      selectedQuotes.value[inquiry.inquiry_id] = {
        provider: validQuotes[0].provider,
        price: validQuotes[0].price as number, // 已经确认不为null
      };
    } else {
      // 没有有效的报价，使用INK的报价，即使是被拒绝的
      selectedQuotes.value[inquiry.inquiry_id] = {
        provider: "INK",
        price: inquiry.quote,
      };
    }
  }
};

// 修改 selectQuote 函数，处理 null 情况
const selectQuote = (
  inquiry: InquiryItem,
  provider: string,
  price: number | null,
) => {
  let priceToStore: number;
  if (price === null) {
    // 通常意味着这是一个被拒绝的外部报价源
    priceToStore = QUOTE_REJECTED;
  } else {
    // price 可能已经是 QUOTE_REJECTED (-1 from INK) 或一个有效数字
    priceToStore = price;
  }
  selectedQuotes.value[inquiry.inquiry_id] = { provider, price: priceToStore };
  expandedQuotes.value[inquiry.inquiry_id] = false;
};

const getTradingRestriction = (providerKey: string): number | undefined => {
  return ProviderTradingRestrictions[
    providerKey as keyof typeof ProviderTradingRestrictions
  ];
};
</script>
<style scoped>
.card-content {
  display: flex;
  flex-direction: column;
}

.inquiry-form {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 10px 20px;
  align-items: center;
  margin: 5px;
}

.form-field {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.form-field label {
  display: flex;
  align-items: center;
  white-space: nowrap;
  padding: 4px 8px;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.form-field label:hover {
  color: var(--el-color-primary);
  background-color: rgba(var(--el-color-primary-rgb), 0.05);
}

.form-field input {
  margin: 0;
}

.form-label {
  position: relative;
}

.required-star {
  color: var(--el-color-error);
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
}

.error-message {
  color: var(--el-color-error);
  text-align: right;
  margin: 0 10px 5px;
}

.card-content .button-group {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
  margin-left: 5px;
}

.validity-hint {
  color: var(--el-text-color-secondary);
  font-size: 13px;
  /* 将提示文字推到左边 */
  margin-right: auto;
  display: flex;
  align-items: center;
  gap: 4px;
}

.validity-hint :deep(.el-icon) {
  font-size: 14px;
}

.secondary-button {
  background-color: var(--el-fill-color-light);
  border: 1px solid var(--el-border-color);
  transition: all 0.25s ease;

  &:hover {
    background-color: var(--el-color-primary-light-3);
    border-color: var(--el-color-primary-light-3);
  }
}

:deep(ul#inquiryStructure-listbox li) {
  text-align: center;
  white-space: pre;
}

:deep(ul#inquiryStructure-listbox li[data-type='call']) {
  color: var(--text-color-call);
}

:deep(ul#inquiryStructure-listbox li[data-type='put']) {
  color: var(--text-color-put);
}

.action-button {
  width: 62px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .data-table {
    min-width: unset;
    width: 100%;
  }

  .order-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .order-row:hover {
    background-color: var(--el-fill-color-light);
  }

  .order-row.selected {
    background-color: var(--el-fill-color);
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  .order-details {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px 12px;
    font-size: 14px;
  }

  .detail-item {
    display: flex;
    padding: 6px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .detail-item:nth-last-child(2),
  .detail-item:last-child {
    border-bottom: none;
  }

  .detail-item .label {
    color: var(--el-text-color-secondary);
    width: 70px;
    flex-shrink: 0;
  }

  .detail-item .value {
    flex-grow: 1;
  }

  /* 隐藏空行但保持其对奇偶计数的影响 */
  tr.dummy-row {
    display: none;
  }

  /* 详情展开动画 */
  .details-transition {
    animation: expand 0.25s ease-in-out forwards;
  }

  @keyframes expand {
    from {
      max-height: 50px;
      opacity: 0.5;
    }

    to {
      max-height: 400px;
      opacity: 1;
    }
  }

  .details-row {
    background-color: var(--el-fill-color-lighter);
  }

  /* 发起询价的移动端样式 */
  .initiate-inquiry .selected-option {
    min-width: 200px;
  }

  .inquiry-form {
    gap: 10px;
  }

  .form-field {
    gap: 4px;
  }

  .button-group {
    padding-top: 4px;
  }

  .validity-hint {
    font-size: 12px;
  }

  .validity-hint :deep(.el-icon) {
    font-size: 13px;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }
}

/* 修改复选框样式 */
.form-field input[type="checkbox"] {
  cursor: pointer;
  border-radius: 3px;
  width: 16px;
  height: 16px;
  border: 1px solid var(--el-border-color-darker);
  appearance: none;
  position: relative;
  transition: all 0.2s ease;
  vertical-align: middle;
  margin-right: 6px;

  &:checked {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }

  &:checked::after {
    content: '';
    position: absolute;
    top: 3px;
    left: 5px;
    width: 3px;
    height: 7px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  &:hover {
    border-color: var(--el-color-primary);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--el-color-primary-rgb), 0.2);
  }
}

/* 结构选择器的样式 */
.structure-field {
  flex-direction: column;
  gap: 12px;
}

.structure-group {
  display: flex;
  align-items: center;
  gap: 16px;
}

.structure-type {
  font-weight: 500;
  width: 42px;
}

.structure-options {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

/* 响应式样式补充 */
@media (max-width: 768px) {
  .structure-group {
    gap: 8px;
  }

  .structure-options {
    gap: 12px;
  }

  .structure-type {
    width: 36px;
  }
}

.call-option {
  color: var(--text-color-call);
}

.put-option {
  color: var(--text-color-put);
}

/* 报价展示样式 */
.quote-column {
  min-width: 100px;
  position: relative;

  &.no-external-quote {
    min-width: 40px;
  }
}

.quote-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;

  &.no-external-quote {
    cursor: unset;
  }
}

.quote-value:not(.no-external-quote):hover {
  background-color: var(--el-fill-color-light);
}

.expand-icon {
  margin-left: 4px;
  font-size: 12px;
  transition: transform 0.3s;
}

.expand-icon.is-expanded {
  transform: rotate(180deg);
}

.quote-details {
  position: absolute;
  z-index: 10;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 160px;
  max-width: 300px;
}

.quote-row {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
  transition: background-color 0.2s;
}

.quote-row:hover {
  background-color: var(--el-fill-color-light);
}

.quote-row.is-selected {
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
  border-radius: 3px;
  position: relative;
}

.provider {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.provider.internal {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.price {
  font-weight: 500;
}

.no-quote {
  color: var(--el-text-color-secondary);
}

/* 展开动画 */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease-out;
  max-height: 300px;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  max-height: 0;
  opacity: 0;
}

/* 移动端报价样式 */
.quote-details-mobile {
  flex-direction: column;
  align-items: flex-start !important;
}

.quote-value-mobile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 2px 0;
  width: 100%;
}

.quote-details-mobile-inner {
  margin-top: 6px;
  padding: 6px;
  border-radius: 4px;
  background-color: var(--el-fill-color-lighter);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

@media (max-width: 768px) {
  .provider-tag {
    font-size: 9px;
    padding: 0 3px;
  }

  .quote-content {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

[v-cloak] {
  display: none;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .expand-icon {
    font-size: 11px;
  }

  .quote-row {
    padding: 3px 0;
  }

  .provider {
    font-size: 12px;
  }
}

/* ... existing mobile styles ... */

/* 修改最优报价样式 */
.quote-row.is-optimal {
  position: relative;
}

.quote-row.is-optimal .price {
  position: relative;
}

.quote-row.is-optimal .price::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background-color: var(--el-color-primary);
}

.provider-tag {
  display: inline-block;
  padding: 0 4px;
  font-size: 10px;
  line-height: 1.5;
  color: var(--el-color-primary-dark-2);
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
  border-radius: 2px;
  margin-right: 4px;
  white-space: nowrap;
}

.quote-content {
  display: flex;
  align-items: center;
}

/* 修改样式，将勾勾放在报价商名和报价之间 */
.quote-row.is-selected {
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
  border-radius: 3px;
}

.quote-row.is-selected .provider {
  position: relative;
  padding-right: 16px;
  color: var(--el-color-primary);
  font-weight: 500;
}

.quote-row.is-selected .provider::after {
  content: '✓';
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: var(--el-color-primary);
  font-weight: bold;
}

/* 反转颜色样式，强调选中的报价商 */
.provider {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 13px;
}

.provider.internal {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

/* 非选中的提供商使用次要颜色 */
.quote-row:not(.is-selected) .provider:not(.internal) {
  color: var(--el-text-color-secondary);
  font-weight: normal;
}

/* 添加地区标签样式 */
.region-tag {
  font-size: 10px;
  color: var(--el-text-color-secondary);
  margin-left: 3px;
}

.trading-tag {
  font-size: 10px;
  color: var(--el-color-warning);
  margin-left: 3px;
}

.trading-tag-sm {
  font-size: 9px;
  color: var(--el-color-warning);
  margin-left: 2px;
}
</style>

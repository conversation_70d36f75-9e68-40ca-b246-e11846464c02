version: '3.8'

services:
  # 总控端后端服务
  control-center-backend:
    build:
      context: .
      dockerfile: ./packages/control-center-backend/Dockerfile
    container_name: ${COMPOSE_PROJECT_NAME}-control-center-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3001
      DATABASE_URL: "file:/app/data/control-center.db"
      REDIS_HOST: control-center-redis
      REDIS_PORT: 6379
      JWT_SECRET: ${CONTROL_CENTER_JWT_SECRET}
      FRONTEND_URL: ${CONTROL_CENTER_FRONTEND_URL:-http://localhost:5173}
    volumes:
      - control-center-data:/app/data
      - control-center-logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock:ro # Docker socket for container management
      - ~/.ssh:/app/.ssh:ro # SSH keys for remote server access
    networks:
      - control-center-network
    depends_on:
      - control-center-redis
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.control-center-api.rule=Host(`${CONTROL_CENTER_DOMAIN}`) && PathPrefix(`/api`)"
      - "traefik.http.routers.control-center-api.entrypoints=web"
      - "traefik.http.services.control-center-api.loadbalancer.server.port=3001"

  # 总控端前端服务
  control-center-frontend:
    build:
      context: .
      dockerfile: ./packages/control-center-frontend/Dockerfile
    container_name: ${COMPOSE_PROJECT_NAME}-control-center-frontend
    restart: unless-stopped
    environment:
      VITE_API_URL: ${CONTROL_CENTER_API_URL:-http://localhost:3001}
      VITE_WS_URL: ${CONTROL_CENTER_WS_URL:-ws://localhost:3001}
    networks:
      - control-center-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.control-center.rule=Host(`${CONTROL_CENTER_DOMAIN}`)"
      - "traefik.http.routers.control-center.entrypoints=web"
      - "traefik.http.services.control-center.loadbalancer.server.port=80"

  # Redis 服务（用于任务队列和缓存）
  control-center-redis:
    image: redis:7-alpine
    container_name: ${COMPOSE_PROJECT_NAME}-control-center-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${CONTROL_CENTER_REDIS_PASSWORD}
    volumes:
      - control-center-redis-data:/data
    networks:
      - control-center-network

  # Traefik 反向代理（如果需要独立的）
  control-center-traefik:
    image: traefik:latest
    container_name: ${COMPOSE_PROJECT_NAME}-control-center-traefik
    restart: unless-stopped
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedByDefault=false"
      - "--entrypoints.web.address=:80"
    ports:
      - "${CONTROL_CENTER_HTTP_PORT:-80}:80"
      - "${CONTROL_CENTER_TRAEFIK_PORT:-8080}:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - control-center-network

networks:
  control-center-network:
    driver: bridge

volumes:
  control-center-data:
  control-center-logs:
  control-center-redis-data: 
{"name": "@packages/monorepo", "version": "1.0.0", "private": true, "type": "module", "dependencies": {"dotenv": "^16.5.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@rsbuild/core": "^1.3.21", "@rsbuild/plugin-sass": "^1.3.1", "@rsbuild/plugin-vue": "^1.0.7", "@types/node": "^22.15.21", "directory-tree": "^3.5.2", "nodemon": "^3.1.10", "tsup": "^8.5.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "scripts": {"dev": "pnpm -r --parallel dev", "dev:server": "pnpm --filter @packages/server dev", "dev:client": "pnpm --filter @packages/client dev", "dev:admin": "pnpm --filter @packages/admin dev", "dev:shared": "pnpm --filter @packages/shared dev", "build": "pnpm -r build", "build:shared": "pnpm --filter @packages/shared build", "build:client": "pnpm --filter @packages/client build", "build:server": "pnpm --filter @packages/server build", "build:admin": "pnpm --filter @packages/admin build", "generate-structure": "tsx scripts/generate-structure.ts", "field-crypto": "pnpm --filter @packages/server field-crypto", "channel-balance-crypto": "pnpm --filter @packages/server channel-balance-crypto", "prisma": "pnpm --filter @packages/server prisma", "generate-agreements": "pnpm --filter @packages/server generate-agreements", "cleanup-logs": "pnpm --filter @packages/server cleanup-logs"}}
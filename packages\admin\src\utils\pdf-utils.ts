import { PDFDocument } from "pdf-lib";
import type { PDFPage } from "pdf-lib";

/**
 * 将印章嵌入到PDF中
 */
async function embedStamp(
	pdfDoc: PDFDocument,
	page: PDFPage,
	stampPngData: ArrayBuffer | Uint8Array,
	position: {
		x: number; // 印章中心点的 X 坐标
		y: number; // 印章中心点的 Y 坐标
	},
) {
	const image = await pdfDoc.embedPng(stampPngData);

	// 固定印章参数
	const STAMP_WIDTH = 80; // 印章宽度（点），约 28.22mm
	const STAMP_OPACITY = 0.7;

	// 根据固定宽度计算等比例高度
	const stampHeight = (STAMP_WIDTH * image.height) / image.width;

	// 以中心点为基准计算左下角坐标
	const x = position.x - STAMP_WIDTH / 2;
	const y = position.y - stampHeight / 2;

	page.drawImage(image, {
		x,
		y,
		width: STAMP_WIDTH,
		height: stampHeight,
		opacity: STAMP_OPACITY,
	});
}

/**
 * 嵌入电子签名到PDF中
 */
async function embedSignature(
	pdfDoc: PDFDocument,
	page: PDFPage,
	position: {
		x: number; // 签名中心点的 X 坐标
		y: number; // 签名中心点的 Y 坐标
	},
	signatureBase64: string,
) {
	// 处理数据URL格式
	const base64Data = signatureBase64.includes("base64,")
		? signatureBase64.split("base64,")[1]
		: signatureBase64;

	if (!base64Data) {
		throw new Error("无效的签名数据格式");
	}

	const signatureBytes = Uint8Array.from(atob(base64Data), (c) =>
		c.charCodeAt(0),
	);
	const signatureImage = await pdfDoc.embedPng(signatureBytes);

	// 固定签名参数
	const SIGNATURE_WIDTH = 40;
	const signatureHeight =
		(SIGNATURE_WIDTH * signatureImage.height) / signatureImage.width;

	// 以中心点为基准计算左下角坐标
	const x = position.x - SIGNATURE_WIDTH / 2;
	const y = position.y - signatureHeight / 2;

	page.drawImage(signatureImage, {
		x,
		y,
		width: SIGNATURE_WIDTH,
		height: signatureHeight,
	});
}

/**
 * 生成ISDA主协议
 */
export async function generateIsdaMasterAgreement(
	templatePdf: ArrayBuffer,
	signature: string,
	stampPngData: ArrayBuffer | Uint8Array,
	customPositions?: {
		stamp?: Array<[number, number, number]>; // [页码, X坐标, Y坐标]
		signature?: Array<[number, number, number]>; // [页码, X坐标, Y坐标]
	},
): Promise<Blob> {
	const pdfDoc = await PDFDocument.load(templatePdf);
	const pages = pdfDoc.getPages();

	// 判断是否使用自定义模板但没有配置位置
	const isCustomTemplate = customPositions !== undefined;
	const hasCustomStampPositions =
		customPositions?.stamp && customPositions.stamp.length > 0;

	// 只有在有自定义印章位置或非自定义模板时才添加印章
	if (hasCustomStampPositions && customPositions?.stamp) {
		// 使用自定义的印章位置
		for (const [pageIndex, x, y] of customPositions.stamp) {
			if (pageIndex >= 0 && pageIndex < pages.length) {
				const page = pages[pageIndex];
				await embedStamp(pdfDoc, page, stampPngData, { x, y });
			}
		}
	} else if (!isCustomTemplate) {
		// 使用默认的印章位置（向后兼容）
		// 确保页面在文档范围内
		if (pages.length > 43) {
			const page37 = pages[36]; // 基于0的索引
			const page44 = pages[43];

			// 定义印章位置
			const stampPositions = [
				{ page: page37, x: 125, y: 725 },
				{ page: page37, x: 355, y: 725 },
				{ page: page44, x: 120, y: 280 },
				{ page: page44, x: 390, y: 280 },
			];

			// 添加印章
			for (const { page, x, y } of stampPositions) {
				await embedStamp(pdfDoc, page, stampPngData, { x, y });
			}
		}
	}

	// 判断是否有自定义签名位置
	const hasCustomSignaturePositions =
		customPositions?.signature &&
		customPositions.signature.length > 0 &&
		signature !== undefined &&
		signature !== null;

	if (hasCustomSignaturePositions && customPositions?.signature && signature) {
		// 使用自定义的签名位置
		for (const [pageIndex, x, y] of customPositions.signature) {
			if (pageIndex >= 0 && pageIndex < pages.length) {
				const page = pages[pageIndex];
				await embedSignature(pdfDoc, page, { x, y }, signature);
			}
		}
	} else if (!isCustomTemplate && signature) {
		// 使用默认的签名位置（向后兼容）
		// 确保页面在文档范围内
		if (pages.length > 43) {
			const page37 = pages[36]; // 基于0的索引
			const page44 = pages[43];

			// 定义两页上的签名位置
			const signaturePositions = [
				{ x: 230, y: 725, page: page37 },
				{ x: 460, y: 725, page: page37 },
				{ x: 220, y: 280, page: page44 },
				{ x: 500, y: 280, page: page44 },
			];

			// 在指定位置添加签名
			try {
				for (const { x, y, page } of signaturePositions) {
					await embedSignature(pdfDoc, page, { x, y }, signature);
				}
			} catch (error) {
				console.error("处理签名图像时出错:", error);
				throw new Error(
					"处理签名图像失败。请确保它是有效的 PNG 格式的 base64 编码图像。",
				);
			}
		}
	}

	// 生成最终的 PDF
	const pdfBytes = await pdfDoc.save();
	return new Blob([pdfBytes], { type: "application/pdf" });
}

/**
 * 生成ISDA补充协议
 */
export async function generateIsdaSupplement(
	templatePdf: ArrayBuffer,
	signature: string,
	stampPngData: ArrayBuffer | Uint8Array,
	customPositions?: {
		stamp?: Array<[number, number, number]>; // [页码, X坐标, Y坐标]
		signature?: Array<[number, number, number]>; // [页码, X坐标, Y坐标]
	},
): Promise<Blob> {
	const pdfDoc = await PDFDocument.load(templatePdf);
	const pages = pdfDoc.getPages();

	// 判断是否使用自定义模板但没有配置位置
	const isCustomTemplate = customPositions !== undefined;
	const hasCustomStampPositions =
		customPositions?.stamp && customPositions.stamp.length > 0;

	// 只有在有自定义印章位置或非自定义模板时才添加印章
	if (hasCustomStampPositions && customPositions?.stamp) {
		// 使用自定义的印章位置
		for (const [pageIndex, x, y] of customPositions.stamp) {
			if (pageIndex >= 0 && pageIndex < pages.length) {
				const page = pages[pageIndex];
				await embedStamp(pdfDoc, page, stampPngData, { x, y });
			}
		}
	} else if (!isCustomTemplate) {
		// 使用默认的印章位置（向后兼容）
		// 确保页面在文档范围内
		if (pages.length > 3) {
			const page = pdfDoc.getPages()[3];
			await embedStamp(pdfDoc, page, stampPngData, {
				x: 210,
				y: 570,
			});
		}
	}

	// 判断是否有自定义签名位置
	const hasCustomSignaturePositions =
		customPositions?.signature &&
		customPositions.signature.length > 0 &&
		signature !== undefined &&
		signature !== null;

	if (hasCustomSignaturePositions && customPositions?.signature && signature) {
		// 使用自定义的签名位置
		for (const [pageIndex, x, y] of customPositions.signature) {
			if (pageIndex >= 0 && pageIndex < pages.length) {
				const page = pages[pageIndex];
				await embedSignature(pdfDoc, page, { x, y }, signature);
			}
		}
	} else if (!isCustomTemplate && signature) {
		// 使用默认的签名位置（向后兼容）
		// 确保页面在文档范围内
		if (pages.length > 3) {
			const page = pdfDoc.getPages()[3];
			// 定义签名位置
			const signaturePositions = [
				{
					x: 450,
					y: 570,
				},
			];

			// 在指定位置添加签名
			try {
				for (const { x, y } of signaturePositions) {
					await embedSignature(pdfDoc, page, { x, y }, signature);
				}
			} catch (error) {
				console.error("处理签名图像时出错:", error);
				throw new Error(
					"处理签名图像失败。请确保它是有效的 PNG 格式的 base64 编码图像。",
				);
			}
		}
	}

	// 生成最终的 PDF
	const pdfBytes = await pdfDoc.save();
	return new Blob([pdfBytes], { type: "application/pdf" });
}

#!/bin/bash

# 总控端快速设置脚本
# 用法: ./scripts/setup-control-center.sh [dev|prod]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低，需要 18+，当前版本: $(node --version)"
        exit 1
    fi
    
    # 检查 pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm 未安装，正在安装..."
        npm install -g pnpm
    fi
    
    # 检查 Docker (仅生产环境)
    if [ "$1" = "prod" ]; then
        if ! command -v docker &> /dev/null; then
            log_error "Docker 未安装，请先安装 Docker"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            log_error "Docker Compose 未安装，请先安装 Docker Compose"
            exit 1
        fi
    fi
    
    log_success "依赖检查完成"
}

# 安装项目依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 安装根目录依赖
    pnpm install
    
    log_success "依赖安装完成"
}

# 设置环境变量
setup_environment() {
    local env_type=$1
    log_info "设置环境变量 ($env_type)..."
    
    # 后端环境变量
    if [ ! -f "packages/control-center-backend/.env" ]; then
        cp packages/control-center-backend/env.example packages/control-center-backend/.env
        log_info "已创建后端环境变量文件，请根据需要修改"
    fi
    
    # 根目录环境变量（Docker）
    if [ "$env_type" = "prod" ] && [ ! -f ".env" ]; then
        cat > .env << EOF
# 项目名称
COMPOSE_PROJECT_NAME=ink-control-center

# 总控端域名
CONTROL_CENTER_DOMAIN=localhost

# 安全配置
CONTROL_CENTER_JWT_SECRET=$(openssl rand -base64 32)
CONTROL_CENTER_REDIS_PASSWORD=$(openssl rand -base64 16)

# 端口配置
CONTROL_CENTER_HTTP_PORT=3080
CONTROL_CENTER_TRAEFIK_PORT=8080

# API配置
CONTROL_CENTER_API_URL=http://localhost:3001
CONTROL_CENTER_WS_URL=ws://localhost:3001
EOF
        log_success "已创建 Docker 环境变量文件"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    cd packages/control-center-backend
    
    # 生成 Prisma 客户端
    pnpm prisma:generate
    
    # 推送数据库结构
    pnpm prisma:push
    
    cd ../..
    
    log_success "数据库初始化完成"
}

# 开发环境启动
start_development() {
    log_info "启动开发环境..."
    
    # 创建启动脚本
    cat > start-dev.sh << 'EOF'
#!/bin/bash

# 启动总控端开发环境

echo "🚀 启动总控端开发环境..."

# 启动后端
echo "📡 启动后端服务..."
cd packages/control-center-backend
pnpm dev &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 启动前端
echo "🎨 启动前端服务..."
cd ../control-center-frontend
pnpm dev &
FRONTEND_PID=$!

echo "✅ 开发环境启动完成"
echo "📱 前端地址: http://localhost:5173"
echo "🔧 后端地址: http://localhost:3001"
echo "📊 健康检查: http://localhost:3001/health"

# 等待用户中断
wait $BACKEND_PID $FRONTEND_PID
EOF
    
    chmod +x start-dev.sh
    
    log_success "开发环境配置完成"
    log_info "运行 './start-dev.sh' 启动开发环境"
}

# 生产环境启动
start_production() {
    log_info "启动生产环境..."
    
    # 构建并启动 Docker 容器
    docker-compose -f docker-compose.control-center.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 健康检查
    if curl -f http://localhost:3001/health > /dev/null 2>&1; then
        log_success "生产环境启动成功"
        log_info "🌐 访问地址: http://localhost:3080"
        log_info "📊 健康检查: http://localhost:3001/health"
        log_info "🔧 Traefik 面板: http://localhost:8080"
    else
        log_error "服务启动失败，请检查日志"
        docker-compose -f docker-compose.control-center.yml logs
        exit 1
    fi
}

# 主函数
main() {
    local env_type=${1:-dev}
    
    echo "🎯 总控端设置脚本"
    echo "📋 环境类型: $env_type"
    echo ""
    
    # 检查参数
    if [ "$env_type" != "dev" ] && [ "$env_type" != "prod" ]; then
        log_error "无效的环境类型: $env_type"
        echo "用法: $0 [dev|prod]"
        exit 1
    fi
    
    # 执行设置步骤
    check_dependencies "$env_type"
    install_dependencies
    setup_environment "$env_type"
    init_database
    
    if [ "$env_type" = "dev" ]; then
        start_development
    else
        start_production
    fi
    
    echo ""
    log_success "🎉 总控端设置完成！"
    
    if [ "$env_type" = "dev" ]; then
        echo ""
        echo "📝 下一步:"
        echo "  1. 编辑环境变量: packages/control-center-backend/.env"
        echo "  2. 启动开发环境: ./start-dev.sh"
        echo "  3. 访问前端: http://localhost:5173"
    else
        echo ""
        echo "📝 管理命令:"
        echo "  查看日志: docker-compose -f docker-compose.control-center.yml logs -f"
        echo "  停止服务: docker-compose -f docker-compose.control-center.yml down"
        echo "  重启服务: docker-compose -f docker-compose.control-center.yml restart"
    fi
}

# 运行主函数
main "$@" 
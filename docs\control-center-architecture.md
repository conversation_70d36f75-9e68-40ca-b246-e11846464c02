# 总控端架构设计

## 🎯 系统概述

总控端（Control Center）是一个独立的运维管理平台，用于统一管理、部署和监控多个应用实例。

## 🏗️ 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    总控端 (Control Center)                    │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Svelte + TypeScript)                            │
│  ├─ 部署管理界面                                              │
│  ├─ 实时监控面板                                              │
│  ├─ 系统配置界面                                              │
│  └─ 操作日志界面                                              │
├─────────────────────────────────────────────────────────────┤
│  Backend (Node.js + Express + TypeScript)                  │
│  ├─ 部署服务 (Deployment Service)                            │
│  ├─ 监控服务 (Monitoring Service)                            │
│  ├─ 代理服务 (Proxy Service)                                 │
│  ├─ WebSocket 服务                                           │
│  └─ 任务调度服务                                              │
├─────────────────────────────────────────────────────────────┤
│  Database (SQLite/PostgreSQL)                              │
│  ├─ 服务器配置                                               │
│  ├─ 部署历史                                                 │
│  ├─ 监控数据                                                 │
│  └─ 操作日志                                                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      目标服务器群                            │
├─────────────────────────────────────────────────────────────┤
│  Server 1                 Server 2                Server N  │
│  ├─ Agent Script          ├─ Agent Script          ├─ ...   │
│  ├─ Docker Compose        ├─ Docker Compose        │        │
│  ├─ INK Application       ├─ INK Application       │        │
│  └─ Monitoring Tools      └─ Monitoring Tools      │        │
└─────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
packages/
├─ control-center-frontend/     # 总控端前端 (Svelte)
├─ control-center-backend/      # 总控端后端 (Node.js)
├─ agent-scripts/              # 服务器代理脚本
├─ client/                     # 现有用户前端
├─ admin/                      # 现有管理后台
├─ server/                     # 现有后端服务
└─ shared/                     # 共享代码
```

## 🚀 实施阶段

### Phase 1: 基础总控端 (2-3周)
- [x] 总控端前后端基础架构
- [x] 基础的服务器配置管理
- [x] 简单的部署脚本执行
- [x] 基本的监控界面

### Phase 2: 智能部署 (2-3周)
- [ ] 环境变量配置界面
- [ ] 选择性子项目部署
- [ ] 部署状态实时反馈
- [ ] 一键更新功能

### Phase 3: 高级监控 (3-4周)
- [ ] 数据库连接池监控
- [ ] 系统资源监控
- [ ] 告警系统
- [ ] 性能分析

### Phase 4: 代理操作 (2-3周)
- [ ] 录单操作代理
- [ ] 系统状态修改
- [ ] 批量操作支持
- [ ] 操作审计

### Phase 5: 多服务器管理 (3-4周)
- [ ] 多服务器配置
- [ ] 跨服务器操作
- [ ] 负载均衡配置
- [ ] 灾备管理

## 🔧 技术栈

### 前端
- **Svelte 4** + TypeScript
- **Vite** 构建工具
- **Tailwind CSS** 样式框架
- **Chart.js** 图表库
- **Socket.io Client** 实时通信

### 后端
- **Node.js** + **Express** + TypeScript
- **Prisma ORM** 数据库操作
- **Socket.io** WebSocket服务
- **Bull Queue** 任务队列
- **PM2** 进程管理
- **node-ssh** SSH操作

### 数据库
- **SQLite** (开发) / **PostgreSQL** (生产)

### 部署
- **Docker** + **Docker Compose**
- **GitHub Actions** CI/CD
- **Nginx** 反向代理

## 🔐 安全考虑

1. **身份认证**: JWT Token + 双因子认证
2. **权限控制**: RBAC 角色权限系统
3. **网络安全**: SSH密钥管理、VPN支持
4. **数据加密**: 敏感配置加密存储
5. **操作审计**: 完整的操作日志记录

## 📊 监控指标

### 应用层监控
- 服务状态 (运行/停止/错误)
- 响应时间和错误率
- API调用统计
- 内存和CPU使用率

### 数据库监控
- 连接池状态
- 查询性能
- 锁等待时间
- 存储空间使用

### 系统监控
- 磁盘空间
- 网络IO
- Docker容器状态
- 日志文件大小

## 🎮 核心功能

### 1. 部署管理
```typescript
interface DeploymentConfig {
  serverId: string;
  projectName: string;
  environment: 'development' | 'staging' | 'production';
  services: string[]; // ['client', 'admin', 'server']
  envVars: Record<string, string>;
  branch: string;
}
```

### 2. 实时监控
```typescript
interface MonitoringData {
  serverId: string;
  timestamp: Date;
  services: ServiceStatus[];
  database: DatabaseMetrics;
  system: SystemMetrics;
}
```

### 3. 代理操作
```typescript
interface ProxyOperation {
  targetService: string;
  operation: 'create' | 'read' | 'update' | 'delete';
  endpoint: string;
  payload?: any;
  headers?: Record<string, string>;
}
```

## 🔄 数据流

1. **部署流程**: 前端配置 → 后端验证 → 生成脚本 → SSH执行 → 状态反馈
2. **监控流程**: Agent收集 → WebSocket推送 → 前端显示 → 告警处理
3. **代理流程**: 前端请求 → 后端转发 → 目标服务 → 结果返回

## 📈 扩展性设计

### 水平扩展
- 支持多个总控端实例
- 数据库读写分离
- 缓存层(Redis)

### 功能扩展
- 插件系统
- 自定义监控指标
- 第三方集成(Slack, 钉钉)
- API开放平台

## 💻 实现指南

### 第一步：创建基础项目结构
```bash
# 创建总控端项目目录
mkdir -p packages/control-center-frontend
mkdir -p packages/control-center-backend
mkdir -p packages/agent-scripts

# 初始化项目
cd packages/control-center-frontend && npm create svelte@latest . --typescript
cd ../control-center-backend && npm init -y
cd ../agent-scripts && npm init -y
```

### 第二步：配置开发环境
- 统一的 TypeScript 配置
- ESLint + Prettier 代码规范
- Husky Git Hooks
- Docker 开发环境

### 第三步：实现核心服务
1. **数据库设计**: 服务器配置、部署历史、监控数据
2. **API 设计**: RESTful API + WebSocket
3. **前端组件**: 响应式布局 + 实时更新
4. **部署脚本**: SSH + Docker 操作

## 🎯 下一步行动计划

### 立即开始 (Phase 1)
1. ✅ 创建项目基础结构
2. ⏳ 搭建总控端后端基础架构
3. ⏳ 搭建总控端前端基础界面
4. ⏳ 实现基础的服务器配置管理
5. ⏳ 创建简单的部署脚本执行功能

### 优先级功能
- **P0**: 基础架构搭建
- **P1**: 服务器配置管理
- **P2**: Docker 选择性部署
- **P3**: 实时监控面板
- **P4**: 一键更新功能

## 🔧 开发工具链

### 代码质量
```json
{
  "eslint": "代码静态检查",
  "prettier": "代码格式化",
  "husky": "Git 钩子",
  "lint-staged": "暂存区检查",
  "commitizen": "规范化提交"
}
```

### 测试策略
- **单元测试**: Jest + Testing Library
- **集成测试**: Supertest (API)
- **E2E测试**: Playwright
- **性能测试**: Artillery

### 监控工具
- **日志**: Winston + 日志轮转
- **指标**: Prometheus + Grafana
- **告警**: 自定义告警规则
- **追踪**: 操作审计日志

## 📚 参考资源

### 相关技术文档
- [Svelte 官方文档](https://svelte.dev/docs)
- [Docker Compose 指南](https://docs.docker.com/compose/)
- [Node-SSH 使用指南](https://github.com/steelbrain/node-ssh)
- [Prisma ORM 文档](https://www.prisma.io/docs)

### 最佳实践
- [12-Factor App](https://12factor.net/)
- [DevOps 最佳实践](https://devops.com/best-practices/)
- [微服务架构模式](https://microservices.io/)
- [监控系统设计](https://sre.google/books/)
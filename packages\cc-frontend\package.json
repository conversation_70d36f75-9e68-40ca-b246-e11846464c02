{"name": "@ink/cc-frontend", "version": "1.0.0", "description": "总控端前端界面", "private": true, "scripts": {"dev": "rsbuild dev", "build": "rsbuild build", "preview": "rsbuild preview", "check": "svelte-check", "check:watch": "svelte-check --watch", "test": "vitest", "test:ui": "vitest --ui", "lint": "prettier --check . && eslint .", "lint:fix": "prettier --write . && eslint . --fix"}, "devDependencies": {"@types/chart.js": "^2.9.41", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-svelte": "^2.35.1", "postcss": "^8.4.31", "prettier": "^3.1.0", "prettier-plugin-svelte": "^3.1.2", "svelte": "^4.2.7", "svelte-check": "^3.6.0", "tailwindcss": "^3.3.5", "tslib": "^2.6.2", "typescript": "^5.3.2", "vitest": "^0.34.6", "@rsbuild/core": "^1.0.0", "@rsbuild/plugin-svelte": "^1.0.10"}, "dependencies": {"chart.js": "^4.4.0", "socket.io-client": "^4.7.4", "axios": "^1.6.2", "js-cookie": "^3.0.5", "date-fns": "^2.30.0", "lucide-svelte": "^0.292.0", "clsx": "^2.0.0"}, "type": "module", "keywords": ["svelte", "sveltekit", "control-center", "frontend"], "author": "", "license": "MIT"}
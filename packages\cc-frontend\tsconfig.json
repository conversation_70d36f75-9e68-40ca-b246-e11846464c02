{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "allowJs": true, "checkJs": true, "isolatedModules": true, "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": false, "sourceMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"$components/*": ["src/lib/components/*"], "$stores/*": ["src/lib/stores/*"], "$utils/*": ["src/lib/utils/*"], "$types/*": ["src/lib/types/*"]}}, "include": ["src/**/*.ts", "src/**/*.js", "src/**/*.svelte"], "exclude": ["node_modules", "dist"]}
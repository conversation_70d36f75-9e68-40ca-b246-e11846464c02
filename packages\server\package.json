{"name": "@packages/server", "private": true, "type": "module", "dependencies": {"@packages/shared": "workspace:*", "@prisma/client": "^6.8.2", "async-mutex": "^0.5.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "bullmq": "^5.53.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "multer": "1.4.5-lts.1", "mysql2": "^3.14.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "pdf-lib": "^1.17.1", "pino": "^9.7.0", "pino-roll": "^3.1.0", "twilio": "^5.6.1", "uuid": "^11.1.0", "ws": "^8.18.2", "zod": "^3.25.21"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/express-serve-static-core": "^5.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.21", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/qs": "^6.14.0", "@types/ws": "^8.18.1", "pino-pretty": "^13.0.0", "prisma": "^6.8.2"}, "scripts": {"build": "tsup", "dev": "nodemon --watch src -e ts --exec tsx src/app.ts", "start": "node dist/app.js", "field-crypto": "tsx src/scripts/db-field-crypto.ts", "channel-balance-crypto": "tsx src/scripts/channel-balance-crypto.ts", "prisma": "tsx src/scripts/prisma-cmd.ts", "generate-agreements": "tsx src/scripts/generate-agreements.ts", "cleanup-logs": "tsx src/scripts/cleanup-logs.ts"}}
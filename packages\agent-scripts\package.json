{"name": "@ink/agent-scripts", "version": "1.0.0", "description": "服务器代理脚本集合", "type": "module", "scripts": {"build": "tsc", "start": "node dist/agent.js", "dev": "tsx watch src/agent.ts", "install-agent": "node scripts/install-agent.js", "test": "jest"}, "bin": {"ink-agent": "./dist/cli.js"}, "dependencies": {"commander": "^11.1.0", "axios": "^1.6.2", "ws": "^8.14.2", "node-cron": "^3.0.3", "systeminformation": "^5.21.20", "dockerode": "^4.0.2", "simple-git": "^3.21.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "pidusage": "^3.0.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/ws": "^8.5.10", "@types/node-cron": "^3.0.11", "tsx": "^4.6.0", "typescript": "^5.3.2", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["agent", "monitoring", "deployment", "server-management"], "author": "", "license": "MIT"}
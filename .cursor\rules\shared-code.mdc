---
description: 
globs: packages/shared/src/**
alwaysApply: false
---
## Shared Package Conventions

*   **Purpose:** This package contains code (types, enums, schemas, constants, simple utils) shared between `client`, `admin`, and `server`.
*   **Validation:** Use `zod` for defining data schemas and performing validation. Place schemas in `src/schemas`. Export both the schema and the inferred TypeScript type.
*   **Types:** Define shared TypeScript interfaces and enums here. Ensure clear naming and export them for use in other packages.
*   **Utilities:** Include only simple, framework-agnostic utility functions here (e.g., date formatting if needed universally). Complex or framework-dependent utilities belong in their respective packages.
*   **Dependencies:** Keep dependencies minimal. Avoid adding dependencies specific to frontend or backend frameworks.


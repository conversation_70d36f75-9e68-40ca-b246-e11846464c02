import { defineConfig } from "@rsbuild/core";
import { pluginSvelte } from "@rsbuild/plugin-svelte";
import path from "node:path";

export default defineConfig({
	plugins: [pluginSvelte()],
	source: {
		entry: {
			index: "./src/main.ts",
		},
		alias: {
			$components: path.resolve(__dirname, "src/lib/components"),
			$stores: path.resolve(__dirname, "src/lib/stores"),
			$utils: path.resolve(__dirname, "src/lib/utils"),
			$types: path.resolve(__dirname, "src/lib/types"),
		},
	},
	html: {
		template: "./src/app.html",
	},
	// 移除tools.postcss配置，让rsbuild自动加载postcss.config.js
});
